/*
 * ━━━━━━神兽出没━━━━━━
Ho~　★★★★★★
　　○★★★★★★★○	
　　★★　　　 　★★	
　★★　∩　　∩　 ★★	
　★★　　　●　 　★★	
　★★　　　　　　★★	
　　★★　　　　★★	
　　　　★★★★　　　◢◤	
　　╭　〡〡〡〡　╮╱	
　　　—┘—┘└—└—
 * ━━━━━━感觉萌萌哒━━━━━━
 */
/**
 * 2017/01/16 16：16
 * 用于Apicloud开发的通用js
 * by 半颗糖。
 */
(function(window){
	var T = {};
	T.NET_ERR = '网络不给力，请稍后再试';
	T.NET_OK = '操作成功';
	T.NET_NO = '操作失败，请重试';
	T.JK_ERR = '接口异常，请联系技术';
	T.LOAD_ING = '加载中，请稍候...';
	T.LOAD_MORE = '点击加载更多';
	T.LOAD_ALL = '没有更多了！';
	T.LOAD_NO = '暂无数据';
	T.LOAD_NOT = '页面尚未加载完成，请刷新重试';
	T.fs = null;
	//判断是否是WebView
	T.isWebView = function() {
		var that = this;
		var host = window.location.host;
		var path = window.location.href;
		if (host == "" && ((path.toLowerCase().indexOf('file:///storage') > -1)) || ((path.toLowerCase().indexOf('file:///android_asset') > -1)) || ((path.toLowerCase().indexOf('file:///data') > -1)) || (path.toLowerCase().indexOf('file:///var/') > -1) || (path.toLowerCase().indexOf('contents:///') > -1) || (path.toLowerCase().indexOf('file:///private/') > -1)) {
			return true;
		} else {
			return false;
		}
	};
	//判断是否是APICloud
	T.isAPICloud = function() {
		var that = this;
		if ( typeof api !== 'undefined' && typeof api.openWin !== 'undefined' && that.isWebView()) {
			return true;
		} else {
			return false;
		}
	};
	//是否为参数
	T.isParameters = function(obj) {
		return obj != null && obj != undefined;
	};
	//对象是否为	某种类型
	T.isTargetType = function(obj, typeString) {
		return typeof obj === typeString;
	};
	//是否为方法 
	T.isFunction = function(obj) {
		var that = this;
		return that.isTargetType(obj, "function");
	};
	//是否为字符串
	T.isString = function(obj) {
		var that = this;
		return that.isTargetType(obj, "string") && obj != null && obj != undefined;
	};
	//是否为 正整数 数字
	T.isPositiveInteger = function(_int) {
		var that = this;
		var reg = /^[\d]+$/;
		return reg.test(_int) && _int != null && _int != undefined;
	};
	//是否为数组
	T.isArray = function(arr) {
		return (toString.apply(arr) === '[object Array]') || arr instanceof NodeList;
	};
	//是否为对象
	T.isObject = function(obj) {
		var that = this;
		return (that.isTargetType(obj, "object") && obj != null && obj != undefined);
	};
	/**
	 * 页面开始函数任何页面必须调用
	 */
	T.ready = function(callback) {
		var that = this;
		if (that.isWebView()) {
			apiready = function() {
				if (that.isFunction(callback)) {
					callback();
				}
			};
		} else {
			window.onload = function() {
				if (that.isFunction(callback)) {
					callback();
				}
			}
		}
	};
	//TODO ######################### 小工具
	/**
	 * 随机 10亿中的一位数	一般用于字符串唯一性
	 */
	T.getNum = function() {
		var that = this;
		return Math.floor(Math.random()*1000000000);
	};
	/**
	 * 直达页面顶部
	 */
	T.goPageTop = function(){
		var that = this;
		if(that.isAPICloud()){
			//调用apicloud返回顶部方法
			api.pageUp({top:true});
		}else{
			//原生js方法 出处：https://www.cnblogs.com/Crysta1/p/6179907.html
			(function smoothscroll(){
				var currentScroll = document.documentElement.scrollTop || document.body.scrollTop;
				if (currentScroll > 0) {
					 window.requestAnimationFrame(smoothscroll);
					 window.scrollTo (0,currentScroll - (currentScroll/5));
				}
			})();
		}
	};
	/**
	 * 到DOM位置
	 */
	T.toDomTop = function(_dom){
		var that = this;
		(function smoothscroll(){
			var currentScroll = _dom.scrollTop;
			if (currentScroll > 0) {
				 window.requestAnimationFrame(smoothscroll);
				 _dom.scrollTop = currentScroll - (currentScroll/5)
			}
		})();
	};
	/**
	 * 直达页面底部
	 */
	T.goPageButtom = function(){
		var that = this;
		if(that.isAPICloud()){
			//调用apicloud直达底部方法
			api.pageDown({bottom:true});
		}else{
			//原生js方法 动画版没找到 后面再说
			document.getElementsByTagName('BODY')[0].scrollTop=document.getElementsByTagName('BODY')[0].scrollHeight;
		}
	};
	/**
	 * 取系统当前时间
	 */
	T.getTime = function(_data){
		var that = this;
		var nowTime = new Date();
		if(_data){
			if(that.isString(_data)){
				nowTime = new Date(_data);
			}else{
				nowTime = _data;
			}
		}
		var year = nowTime.getFullYear();
		var month = nowTime.getMonth() + 1;//月份从0开始的
		var day = nowTime.getDate();
		var hours = nowTime.getHours();
		var minutes = nowTime.getMinutes();
		var seconds = nowTime.getSeconds();
		month = (month < 10)?"0"+month:month;
		day = (day < 10)?"0"+day:day;
		hours = (hours < 10)?"0"+hours:hours;
		minutes = (minutes < 10)?"0"+minutes:minutes;
		seconds = (seconds < 10)?"0"+seconds:seconds;
		return year+"-"+month+"-"+day+" "+hours+":"+minutes+":"+seconds;
	}
	/**
	 * 一个旧json 和一个新json 合成一个新的json 默认替换 param【旧对象，新对象，传true或1 就不替换】
	 */
	T.setNewJSON = function(obj,newobj,_ifReplace){
		//重写 不然一个页面写2次 比如framegroup的时候	就会重复的问题
		var returnObj = {};
		for(var key in obj){
			returnObj[key] = obj[key];
		}
		for(var key in newobj){
			if(_ifReplace){
				if(returnObj.hasOwnProperty(key)) //加上就不替换	不加就替换
					continue;
			}
			returnObj[key] = newobj[key];
		}
		return returnObj;
	}
	/**
	 * 去掉html所有标签
	 */
	T.delHtmlTag = function(str) {
		return str.replace(/<[^>]+>/g, "");
	}
		
	//TODO ######################### apicloud方法 必须判断在网页中或app中的2种情况做处理
	
	/**
	 * 设备唯一标识，字符串类型 
	 */
	T.deviceId = function(){
		var that = this;
		if (that.isAPICloud()) {
			return api.deviceId;
		}else{
			return "0";
		}
	};
	/**
	 * 打开win-----name页面名字,url页面地址,pageParam传值参数,reload重新载入时是否刷新,allowEdit是否可长按复制
	 */
	T.openWin = function(name,url,_pageParam,reload,allowEdit) {
		var that = this;
		var delay=0;
		var pageParam = _pageParam?_pageParam:{};
		var o = {
			name:name,
			url:url,
			pageParam:pageParam,
			bgColor:"#FFF",
			bounces:false,
			slidBackEnabled:false,//ios滑动返回
			hScrollBarEnabled:false,//是否显示水平滚动条
			animation : {
				type : "push",
				subType:"from_right",
				duration:300
			},
			reload:reload,	// 是否可以刷新
			allowEdit:allowEdit,	// 是否可以编辑
			delay:delay,
			overScrollMode:"scrolls"
		};
		if (that.isAPICloud()) {
			if(!that.fs){
				that.fs = api.require('fs');
			}
			if(o.url.indexOf('http') != 0){
				var ifExist = that.fs.existSync({path: o.url});
				if(!ifExist.exist || ifExist.directory){//如果不存在 并且为文件夹 就不跳转 这链接有问题
					that.toast("这个链接有问题："+o.url);
					return;
				}
			}
			if(api.systemType == 'ios'){
				o.delay = 0;
			}else{
				o.delay = 50;
			}
			api.openWin(o);
		} else {
			if(!pageParam.link && url.indexOf('http') != 0){//不是外部链接 才走这个通道  不是打开网页
				if(!pageParam.pt){
					if(!pageParam.userId && vm.pageParam.userId) pageParam.userId = vm.pageParam.userId;
					if(!pageParam.sysUrl && vm.pageParam.sysUrl) pageParam.sysUrl = vm.pageParam.sysUrl;
					if(!pageParam.sysAreaId && vm.pageParam.sysAreaId) pageParam.sysAreaId = vm.pageParam.sysAreaId;
				}
				if(!pageParam.appTheme && vm.pageParam.appTheme) pageParam.appTheme = vm.pageParam.appTheme;
				if(!pageParam.iszx && vm.pageParam.iszx) pageParam.iszx = vm.pageParam.iszx;
				if(!pageParam.closewx && vm.pageParam.closewx) pageParam.closewx = vm.pageParam.closewx;
				if(!pageParam.only && vm.pageParam.only) pageParam.only = vm.pageParam.only;
				//处理 网页上点击跳转的参数 和链接
				var shareText = "";
				for(var _eItem in pageParam)
					shareText += (shareText?"-zyz-":"") + (_eItem + "-zy-" + pageParam[_eItem]);
				var myHref = document.location.href;
				var oldHref = myHref.substring(0,myHref.indexOf('html/'));
				var oldUrl = url.substring(url.indexOf('html/'));
				url = oldHref + oldUrl + "?ndata=" + shareText;
			}
			if (parent && parent.window) {
				parent.window.location = url;
			} else {
				window.location = url;
			}
		}
	};
	/**
	 * 关闭win-----name要关闭的名字 不写则关闭当前	animation关闭页面动画效果 传none则不要动画
	 */
	T.closeWin = function(name,animation) {
		var that = this;
		var o = {};
		if (that.isAPICloud()) {
			if (name) {
				o.name = name;
			}
//			o.animation = animation=="none"?{type:"none"}:{type:"push",subType:"from_"+(animation?animation:"right"),duration:300};
			api.closeWin(o);
		} else {
			try {
				parent.window.history.go(-1);
			} catch (e) {
				window.history.go(-1);
			}
		}
	};
	/**
	 *	打开frame(有特殊大小的frame请自行调用api.openFrame处理)-----name页面名字,url页面地址,pageParam传值参数,reload重新载入时是否刷新,allowEdit是否可长按复制,marginTop头部距离顶部多少,marginBottom底下距离多少,animation动画划动边 top right bottom left,progress传page时为进度条
	 */
	T.openFrame = function(name,url,pageParam,reload,allowEdit,marginTop,marginBottom,animation,progress) {
		var that = this;
		var anima = animation?{type:"push",subType:"from_"+animation,duration:300}:{type:"none"};
		var o = {
			name: name,
			url: url,
			rect: {
				x: 0,
				y: 0,
				w: 'auto',
				h: 'auto',
				marginLeft : 0,
				marginTop : marginTop?marginTop:0,
				marginBottom : marginBottom?marginBottom:0,
				marginRight : 0
			},
			pageParam:pageParam?pageParam:{},
			bounces: false,
			hScrollBarEnabled: false,
			scaleEnabled:true,
			progress:{
				type:progress?progress:"default",
				title:"加载中",
				text:"请稍候...",
				color:"#45C01A"
			},
			reload:reload,
			allowEdit:allowEdit,
			animation:anima,
			overScrollMode:"scrolls",
			defaultRefreshHeader:"swipe"
		};
		//适应新写法
		if(that.isObject(name)){
			o = that.setNewJSON(o,name);
		}
		if(!that.fs){
			that.fs = api.require('fs');
		}
		if(o.url.indexOf('http') != 0){
			var ifExist = that.fs.existSync({path: o.url});
			if(!ifExist.exist || ifExist.directory){//如果不存在 并且为文件夹 就不跳转 这链接有问题
				that.toast("这个链接有问题："+o.url);
				return;
			}
		}
		if (that.isAPICloud()) {
			api.openFrame(o);
		}else{
			if ($api.byId("T-frame") != null) {
				$api.attr($api.byId("T-frame"), "src", url);
				return;
			}
			var iframeHtml = '<iframe id="T-frame" src="'+ url +'" class="T-iframe" frameborder="0" scrolling="yes"></iframe>';
			if ($api.dom(".T-main") != null) {
				$api.css($api.dom(".T-main"), "position: relative;-webkit-overflow-scrolling: touch; overflow: scroll;");
				$api.html($api.dom(".T-main"), iframeHtml);
			} else {
				var html = '<div class="T-main T-flex-item" style="position: relative;-webkit-overflow-scrolling: touch; overflow: scroll;">';
				html += iframeHtml;
				html += '</div>';
				if (!$api.hasCls(document.body, "T-flexbox-vertical"))
					$api.addCls(document.body, "T-flexbox-vertical");
				$api.append(document.body, html);
			}
		}
	};
	/**
	 *	关闭frame-----name页面名字
	 */
	T.closeFrame = function(name) {
		var that = this;
		var o = {};
		if (name) {
			o.name = name;
		}
		if (that.isAPICloud()) {
			api.closeFrame(o);
		}
	};
	/**
	 *	frame页面高度 出处：https://www.cnblogs.com/zh-1721342390/p/9604801.html
	 */
	T.frameHeight = function(){
		var that = this;
		if(T.isAPICloud()){
			return api.frameHeight;
		}else{
			return document.body.offsetHeight;
		}
	};
	/**
	 *	获取设备类型
	 */
	T.systemType = function(){
		var that = this;
		if(T.isAPICloud()){
			return api.systemType;
		}else{
			return "";
		}
	};
	/**
	 *	发送事件监听
	 */
	T.sendEvent = function(_param){
		var that = this;
		if(T.isAPICloud()){
			api.sendEvent(_param);
		}
	};
	/**
	 *	设置事件监听-----name名字, callback回调方法
	 */
	T.addEventListener = function(name,callback) {
		var that = this;
		if (that.isAPICloud()) {
			api.addEventListener({name:name,extra:{threshold:50}}, function(ret, err) {
				if (that.isFunction(callback)) {
					callback(ret, err);
				}
			});
		}
	};
	/**
	 *	设置小型存储-----key键, value值
	 */
	T.setPrefs = function(key, value) {
		var that = this;
		if (that.isAPICloud()) {
			api.setPrefs({key:key,value:value});
		}else{
			$api.setStorage(key,value);
		}
	};
	/**
	 *	获取小型存储-----key键
	 */
	T.getPrefs = function(key) {
		var that = this;
		return that.isAPICloud()?api.getPrefs({sync:true,key:key}):$api.getStorage(key);
	};
	/**
	 *	删除小型存储-----key键
	 */
	T.removePrefs = function(key) {
		var that = this;
		if (that.isAPICloud()) {
			api.removePrefs({key:key});
		}else{
			$api.rmStorage(key);
		}
	};
	
	/**
	 *	设置弹出提示-----msg内容, location位置	top middle	 global是否全局弹出
	 */
	T.toast = function(msg,location,global){
		var that = this;
		if (that.isAPICloud()) {
			api.toast({
				msg:msg,
				duration:2000,
				location:location ? location : 'middle',
				global:global
			});
		}else{
			try{vant.Toast({duration: 2000,message:msg});}catch(e){}
		}
	}
	/**
	 * 页面监听向右滑动关闭界面	可以得到返回监听 自己写关闭操作
	 */
	T.closeListener = function(callback){
		var that = this;
		if (that.isAPICloud()) {
			that.addEventListener('swiperight',function(ret,err){
				if (that.isFunction(callback)) {
					callback(ret, err);
				}else{
					that.closeWin();
				}
			});
		}
	}
	/**
	 * 页面设置下拉刷新-----param自定义参数,callback刷新时要做的事。加载完成后调用api.refreshHeaderLoadDone()方法恢复组件到默认状态
	 */
	T.setRefreshHeaderInfo = function(_params,callback){
		var that = this;
		var o = {
			loadingImg : 'widget://image/refresh.png',
			bgColor : '#F5F5F5',
			textColor : '#8E8E8E',
			textDown : '下拉可以刷新...',
			textUp : '松开可以刷新...',
			textLoading:'刷新中...',
			textTime:refreshText||"",
			showTime : true
		};
		o = that.setNewJSON(o,_params);
		if (that.isAPICloud()) {
			api.setRefreshHeaderInfo(o, function(ret,err){
				if (that.isFunction(callback)) {
					callback(ret,err);
				}
			});
		}
	}
	/**
	 * 获取winName
	 */
	T.winName = function(){
		var that = this;
		if (that.isAPICloud()) {
			return api.winName;
		}
	}
	/**
	 * 获取frameName 
	 */
	T.frameName = function(){
		var that = this;
		if (that.isAPICloud()) {
			return api.frameName;
		}
	}
	/**
	 * 设置 frame属性
	 */
	T.setFrameAttr = function(_params){
		var that = this;
		var defaultParam = {
			
		};
		defaultParam = that.setNewJSON(defaultParam,_params);
		if (that.isAPICloud()) {
			api.setFrameAttr(defaultParam);
		}
	}
	/**
	 * 图片缓存
	 */
	T.imageCache = function(_params,callback){
		var that = this;
		var defaultParam = {
			url:"",//图片远程地址
			encode:false,//是否对url进行编码。	java的默认不编码
			policy:"default",//缓存策略
			thumbnail:true,//使用缩略图
			//tag:"",//标识信息，将在回调中返回
		};
		defaultParam = that.setNewJSON(defaultParam,_params);
		if (that.isAPICloud()) {
			api.imageCache(defaultParam, function(ret, err) {
//				var img = new Image();
//				img.onload = function() {//图片地址是正确的
//				}
//				img.onerror = function() {//图片地址错误
//				}
//				img.src = ret.url;
				if (that.isFunction(callback)) {
					callback(ret,err);
				}
			});
		}
	}
	
	/**
	 * 通知下拉刷新数据加载完毕，组件会恢复到默认状态
	 */
	T.refreshHeaderLoadDone = function(){
		var that = this;
		if (that.isAPICloud()) {
			api.refreshHeaderLoadDone();
		}else{
			vm.appRefresh.isLoading = false;
		}
	}
	/**
	 * 设置下拉刷新组件为刷新中状态
	 */
	T.refreshHeaderLoading = function(){
		var that = this;
		if (that.isAPICloud()) {
			api.refreshHeaderLoading();
		}else{
			if(!vm.appRefresh.isDisabled && !vm.appRefreshisDisabled){//没有禁用即可刷新
				vm.appRefresh.isLoading = true;
				vm.onRefresh();
			}
		}
	}
	/**
	 * 显示加载框----- title刷新标题,modal是否拟态	true将不可交互
	 */
	T.showProgress = function(title,modal){
		var that = this;
		if (that.isAPICloud()) {
			api.showProgress({
					title: title?title:'加载中',
					text: '请稍候...',
					modal: modal
				});
		}else{
			var o = {
				type:"loading",
				message: "加载中...",
				forbidClick: true,
				duration:0,
			};
			if(that.isObject(title)){
				o = that.setNewJSON(o,title);
			}else{
				o.message = (title?title:'加载中') + "...";
			}
			vant.Toast(o);
		}
	}
	/**
	 * 隐藏进度提示框
	 */
	T.hideProgress = function(){
		var that = this;
		if (that.isAPICloud()) {
			api.hideProgress();
		}else{
			vant.Toast.clear();
		}
	}
	/**
	 * 取消网络请求----- name标识名字
	 */
	T.cancelAjax = function(name){
		var that = this;
		if (that.isAPICloud()) {
			api.cancelAjax({tag:name});
		}
	}
	
	var db="";
	/**
	 * 网络请求----- url请求地址,tag请求标识,callback回调,logtext说明文字,method请求类型,data为post时传入的data
	 * 【2019年4月18日 11:23:59 增加参数 url传入json】 {u:"url"}
	 * u链接	 c是否缓存 ct缓存的时间(默认7秒) t(针对上拉加载更多不缓存请传入1或bottom) returnAll是否返回所有信息	frequency失败重复请求数
	 */
	T.ajax = function(url,tag,callback,logtext,method,data,xhr){
		var that = this;
		var getUrl = url;//请求链接
		var frequency = 0;//网络异常 重复请求次数
		var returnAll = false;//是否返回header等所有信息
		var paramData = {};
		var dotSys = false;
		var json = false;
		var headers = {
			"u-login-areaId":zyUrl.getSiteID(),
			"Authorization":T.getPrefs("Sys_token")
		};
		var appSecret = "8800c670ccc54bb0a9724ff05549f208";
		var clientId = "2c46c1e4f8954b54bb1b984efa06fab6";
		if(that.isObject(url)){
			getUrl = url.u;//请求链接
			frequency = url.frequency || 0;
			paramData = url.paramData || {};
			returnAll = url.returnAll;
			dotSys = url.dotSys;
			json = url.json;
			headers = that.setNewJSON(headers,url.headers || {});
			if(url.a74d11){
				appSecret = "92563132af184d5bb256a32ef2c3a60e";
				clientId = "74d11571f6e349c6b7201b387976c3b7";
			}
		}
		var method = method ? method : "get";
		if(method.toLocaleUpperCase() == "GET" && data){//是get 参数在data 传过来的 加上去
			getUrl = getUrl.indexOf('?') == -1?getUrl+"?":getUrl;
			for(item in data){
				if(item){
					getUrl += "&"+item+"="+data[item]
				}
			}
		}
		if(!dotSys){
			//获取参数名 没有问号时打一个问号
			getUrl = getUrl.indexOf('?') == -1?getUrl+"?":getUrl;
			if(method.toLocaleUpperCase() == "GET"){
				getUrl += (getUrl.charAt(getUrl.length-1) == "&"?"":"&") + "loginAreaId=" + zyUrl.getSiteID() + (userInfo?'&openId=' + userInfo.openid:'');
			}
			var separate = "/lzt";
			if(getUrl.indexOf("/platform-lzt") != -1) separate = "/platform-lzt";
			var signatureAppUrl = getUrl.split(separate)[0];
			var signatureUrl = getUrl.match(/lzt(\S*)\?/)[1];
			var signatureParam = getUrl.split('?')[1];
			var signatureTime = Date.parse(new Date()) / 1000;
			var signatureMessage = signatureTime + '-' + method.toLocaleUpperCase() + '-' + signatureUrl + '-' + getSignatureParam(method, signatureParam);
			var signatureHash = CryptoJS.HmacSHA256(signatureMessage, appSecret).toString();
			if(method.toLocaleUpperCase() == "GET"){
				getUrl = signatureAppUrl + separate + signatureUrl + '?timestamp=' + signatureTime + '&signature=' + signatureHash + '&clientId=' + clientId + '&' + signatureParam;
			}else{
				if(xhr && that.isFunction(xhr)){
					data.append('loginAreaId', zyUrl.getSiteID());
					data.append('timestamp', signatureTime);
					data.append('signature', signatureHash);
					data.append('clientId', clientId);
					if(userInfo)
						data.append('openId', userInfo.openid);
				}else if(json){
					getUrl = signatureAppUrl + separate + signatureUrl + '?timestamp=' + signatureTime + '&signature=' + signatureHash + '&clientId=' + clientId + "&loginAreaId=" + zyUrl.getSiteID();
					if(userInfo)
						getUrl += "&userId="+userInfo.openid;
				}else{
					data.loginAreaId = zyUrl.getSiteID();
					data.timestamp = signatureTime;
					data.signature = signatureHash;
					data.clientId = clientId;
					if(userInfo)
						data.openId = userInfo.openid;
				}
			}
		}
		logtext = "";
		var o = {
			url:getUrl,
			async:true,
			type:method,
			cache:true,
			timeout:30000,
			headers: headers,
			success : function(ret) {
				dealAjaxContent(ret);
				if(logtext)
					console.log("得到"+logtext+"返回结果ret："+JSON.stringify(ret));
				if(that.isFunction(callback)) {
					callback(ret,null);
				}
			},
			error : function(err) {
				if(JSON.stringify(err) != "{}"){//只有错误才进这个异常 代码错误不进
					if(logtext)
						console.error("得到"+logtext+"返回结果err："+JSON.stringify(err));
						//开启网络失败时请求
//					if(frequency > 0){
//						var frequencyUrl = url;
//						frequencyUrl.frequency--;
//						that.ajax(frequencyUrl,tag,callback,logtext,method,data,header);
//						return;
//					}
					if(that.isFunction(callback)) {
						callback(null,err);
					}
				}
			}
		};
		if (that.isObject(data) && o.type == "post") {
			o.data = data;
		}
		if(json){
			o.dataType = "json";
			o.contentType = "application/json";
			o.data = JSON.stringify(data);
		}
		if(xhr && that.isFunction(xhr)){
			o.contentType = false; // 不设置内容类型
			o.processData = false; // 不处理数据
			o.xhr = function(){//回调 进度
				myXhr = $.ajaxSettings.xhr();
				if(myXhr.upload){
					myXhr.upload.addEventListener('progress',function(e) {
						if (e.lengthComputable) {
							var percent = Math.floor(e.loaded/e.total*100);
							if(that.isFunction(callback)) {
								xhr(percent);
							}
						}
					}, false);
				}
				return myXhr;
			}
		}
		o = that.setNewJSON(o,paramData);
		if(logtext){
			if(o.type == "post"){
				console.log(logtext+"post【"+frequency+"】："+JSON.stringify(o));
			}else{
				console.log(logtext+"get【"+frequency+"】："+o.url);
			}
		}
		if(that.isAPICloud()) {
			
		}else{
//			console.log(JSON.stringify(o));
			$.ajax(o);
			
//			axios(o).then(function (res) {
//				var returnData = returnAll?res:res.data;
//				if(logtext)
//					console.log("得到"+logtext+"返回结果ret："+JSON.stringify(returnData));
//				if(that.isFunction(callback)) {
//					callback(returnData,null);
//				}
//			}).catch(function (err) {
//				if(JSON.stringify(err) != "{}"){//只有错误才进这个异常 代码错误不进
//					if(logtext)
//						console.error("得到"+logtext+"返回结果err："+JSON.stringify(err));
//						//开启网络失败时请求
////					if(frequency > 0){
////						var frequencyUrl = url;
////						frequencyUrl.frequency--;
////						that.ajax(frequencyUrl,tag,callback,logtext,method,data,header);
////						return;
////					}
//					if(that.isFunction(callback)) {
//						callback(null,err);
//					}
//				}
//			});
		}
	}
	/**
	 * 弹框	alert 【2019年9月11日 17:13:58】
	 * https://docs.apicloud.com/Client-API/api#33
	 */
	T.alert = function(_msg,callback){
		var that = this;
		var o = {
			title: '提示',
			msg: "",
			buttons:["确定"]
		};
		if(that.isObject(_msg)){
			o = that.setNewJSON(o,_msg);
		}else{
			o.msg = _msg;
		}
		if (that.isAPICloud()) {
			api.alert(o, function(ret, err) {
				if (that.isFunction(callback)) {
					callback(ret,err);
				}
			});
		}else{
			alert(o.msg);
		}
	}
	/**
	 * 弹出带两个或三个按钮的confirm对话框	confirm	【2019年9月11日 17:28:33】
	 * https://docs.apicloud.com/Client-API/api#1
	 */
	T.confirm = function(_param,callback){
		var that = this;
		var o = {
			title: '提示',
			msg: "",
			buttons:['确定', '取消']
		};
		o = that.setNewJSON(o,_param);
		if (that.isAPICloud()) {
			api.confirm(o, function(ret, err) {
				if (that.isFunction(callback)) {
					callback(ret,err);
				}
			});
		}
	}
	/**
	 * 弹出带两个或三个按钮和输入框的对话框	 prompt	【2019年9月11日 17:28:33】
	 * https://docs.apicloud.com/Client-API/api#1
	 */
	T.prompt = function(_param,callback){
		var that = this;
		var o = {
			title: '提示',
			msg: "",
			text:"",
			type:"text",
			buttons:['确定', '取消']
		};
		o = that.setNewJSON(o,_param);
		if (that.isAPICloud()) {
			api.prompt(o, function(ret, err) {
				if (that.isFunction(callback)) {
					callback(ret,err);
				}
			});
		}
	}
	/**
	 * 底部弹出框	actionSheet	【2019年9月11日 17:13:39】
	 * https://docs.apicloud.com/Client-API/api#1
	 */
	T.actionSheet = function(_param,callback){
		var that = this;
		var o = {
				title: '请选择',
				cancelTitle: '取消',
				destructiveTitle:"",
		};
		o = that.setNewJSON(o,_param);
		if (that.isAPICloud()) {
			api.actionSheet(o, function(ret, err) {
				if (that.isFunction(callback)) {
					callback(ret,err);
				}
			});
		}
	}
	/**
	 * APICloud才有的 提供动态检测应用是否已取得某个或多个权限
	 * https://docs.apicloud.com/Client-API/api#hasPermission
	 */
	T.hasPermission = function(one_per){
		var that = this;
		if (that.isAPICloud()) {
			var perms = new Array();
			if(!one_per){
				return;
			}
			if(one_per.indexOf(',') != -1){
				var one_pers = one_per.split(',');
				for(var i = 0; i < one_pers.length; i++){
					perms.push(one_pers[i]);
				}
			}else{
				perms.push(one_per);
			}
			var rets = api.hasPermission({list:perms});
			if(one_per.indexOf(',') != -1){//判断一堆时 就自己看	一般是一个一个判断
				that.alert('判断结果：' + JSON.stringify(rets));
	 			return;
			}else{
				return rets;
			}
		}
	}
	
	/**
	 * APICloud才有的 向系统请求某个或多个权限
	 * https://docs.apicloud.com/Client-API/api#requestPermission
	 */
	T.requestPermission = function(one_per,callback){
		var that = this;
		if (that.isAPICloud()) {
			var perms = new Array();
			if(!one_per){
				return;
			}
			if(one_per.indexOf(',') != -1){
				var one_pers = one_per.split(',');
				for(var i = 0; i < one_pers.length; i++){
					perms.push(one_pers[i]);
				}
			}else{
				perms.push(one_per);
			}
			api.requestPermission({
		 		list: perms,
		 		code: 100001
		 	}, function(ret, err){
		 		if(callback){
		 			callback(ret);
		 			return;
		 		}
//	 		var str = '请求结果：\n';
//	 		str += '请求码: ' + ret.code + '\n';
//	 		str += "是否勾选\"不再询问\"按钮: " + (ret.never ? '是' : '否') + '\n';
//	 		str += '请求结果: \n';
//	 		var list = ret.list;
//	 		for(var i in list){
//	 			str += list[i].name + '=' + list[i].granted + '\n';
//	 		}
//	 		that.alert(str);
		 		console.error(JSON.stringify(ret));
		 	});
		}
	}
	
	/**
	 * APICloud才有的 检测是否有权限 没有就请求
	 */
	T.confirmPer = function(perm){
		var that = this;
		if (that.isAPICloud()) {
			var has = that.hasPermission(perm);
			if(!has || !has[0] || !has[0].granted){
				that.confirm({
					title: '提醒',
					msg: '没有获得 ' + perm + " 权限\n是否前往设置？",
					buttons: ['去设置', '取消']
				}, function(ret, err) {
					if(1 == ret.buttonIndex){
						that.requestPermission(perm);
					}
				});
				return false;
			}
			return true;
		}
	}
	
	var imageBrowser;
	/**
	 * 打开图片浏览器 2019年9月27日 16:11:36	
	 * https://docs.apicloud.com/Client-API/Func-Ext/imageBrowser 文档地址
	 */
	T.openImageBrowser = function(_param){
		var that = this;
		if (that.isAPICloud()) {
			if(!imageBrowser)
				imageBrowser = api.require('imageBrowser');
			var o = {
				imageUrls: [],
				showList: false,
				activeIndex: 0,
				tapClose: false
			};
			o = that.setNewJSON(o,_param);
			console.error("打开图片集合："+JSON.stringify(o.imageUrls));
			if (that.isAPICloud()) {
				imageBrowser.openImages(o);
			}
		}
	}
	
	var UIAlbumBrowser;
	/**
	 * 获取多张图片 
	 */
	T.openUIAlbumBrowser = function(_param,_callback){
		var that = this;
		if (that.isAPICloud()) {
			if(!UIAlbumBrowser)
				UIAlbumBrowser = api.require('UIAlbumBrowser');
			var o = {
				type: "image",
				max: 8,
				isOpenPreview:false,
				classify:false,
				styles: {mark: {size: 25},nav: {bg: '#000'}}
			};
			o = that.setNewJSON(o,_param);
			UIAlbumBrowser.open(o, function( ret ){
				if(_callback){
		 			_callback(ret);
		 		}
			});
		}
	}
	
	/**
	 * getPicture 选择单张图片	
	 * https://docs.apicloud.com/Client-API/api#20
	 */
	T.getPicture = function(_param,_callback){
		var that = this;
		if (that.isAPICloud()) {
			var o = {
				sourceType : "camera",
				encodingType : "jpg",
				mediaValue : "pic",
				targetWidth : 720,
			};
			o = that.setNewJSON(o,_param);
			api.getPicture(o, function(ret, err) {
				if(_callback){
		 			_callback(ret, err);
		 		}
			});
		}
	}
	
	/** 16进制颜色 转换成rgba颜色	可设置透明 */
	T.colorRgba = function(_color,_alpha){
		if(!_color)return;
		// 16进制颜色值的正则
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		// 把颜色值变成小写
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			// 如果只有三位的值，需变成六位，如：#fff => #ffffff
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			// 处理六位的颜色值，转为RGB
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return "rgba(" + colorChange.join(",") + "," + (T.isParameters(_alpha)?_alpha:"1") + ")";
		} else {
			return color;
		}
	}
	//16进制颜色转化为RGB颜色
	T.colorRgb = function(str) {
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var sColor = str.toLowerCase();
		if (sColor && reg.test(sColor)) {
			if (sColor.length === 4) {
				var sColorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
				}
				sColor = sColorNew;
			}
			//处理六位的颜色值
			var sColorChange = [];
			for (var i = 1; i < 7; i += 2) {
				sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
			}
			return "rgb(" + sColorChange.join(",") + ")";
		} else {
			return sColor;
		}
	}
	/** 判断颜色属于深色还是浅色*/
	T.isColorDarkOrLight = function(hexcolor){
		try{
			var colorrgb = T.colorRgb(hexcolor);
			var colors = colorrgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
			var red = colors[1];
			var green = colors[2];
			var blue = colors[3];
			var brightness;
			brightness = (red * 299) + (green * 587) + (blue * 114);
			brightness = brightness / 255000;
			if (brightness >= 0.5) {
				return "light";
			} else {
				return "dark";
			}
		}catch(e){
			return "";
		}
	}
	//新版本actionSheet
	T.nActionSheet = function(_param,callback){
		var that = this;
		var name = _param.onlyName || 'up_actionSheet';
		var o = {title: '请选择',cancelTitle: '取消',onlyCallback:name+'_callback'};
		o = that.setNewJSON(o,_param);
		if (that.isAPICloud()) {
			api.removeEventListener({name:o.onlyCallback});
			T.addEventListener(o.onlyCallback,function(ret,err){
				if (that.isFunction(callback)) {
					callback(ret.value);
				}
			});
			T.openFrame(name,'widget://html/api_dialog/actionSheet.html',o,false,false,0,0);
			T.sendEvent({name:name,extra:{type:"open",param:o}});
		}
	}
	//新版本prompt	兼容alert confirm
	T.nPrompt = function(_param,callback){
		var that = this;
		var name = _param.onlyName || ('up_prompt'+(T.frameName()?T.frameName():T.winName()));
		var o = {title: '',type:"",buttons:['确定','取消'],onlyCallback:name+'_callback'};
		if(T.isString(_param)){
			o.msg = _param;
		}else{
			o = that.setNewJSON(o,_param);
		}
		if (that.isAPICloud()) {
			api.removeEventListener({name:o.onlyCallback});
			T.addEventListener(o.onlyCallback,function(ret,err){
				if (that.isFunction(callback)) {
					callback(ret.value);
				}
			});
			T.openFrame(name,'widget://html/api_dialog/prompt.html',o,false,false,0,0);
			T.sendEvent({name:name,extra:{type:"open",param:o}});
		}
	}
	/*end*/
	window.T = T;
})(window);