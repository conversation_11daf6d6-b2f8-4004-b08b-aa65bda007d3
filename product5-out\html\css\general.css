﻿﻿/*
 * 2015/08/30 15：18
 * 用于Apicloud开发的通用css
 * by 半颗糖。
 */
/*基础参数，手机和电脑上不同*/
@charset "UTF-8";
html,body {-webkit-touch-callout:none;-webkit-text-size-adjust:none;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);}
body {background-color: #F8F8F8;}
html,body,header,section,footer,div,ul,ol,li,img,a,span,em,del,legend,center,var,fieldset,form,label,dl,dt,dd,cite,input,time,mark,code,figcaption,figure,textarea,h1,h2,h3,h4,h5,h6,p{margin:0;border:0;padding:0;font-style:normal;}
nav,article,aside,details,main,header,footer,section,fieldset,figcaption,figure{display: block;}
header,section,footer {position:relative;}
ul,ol,ul{list-style:none;}
input,button,textarea {border:0;margin:0;padding:0;background-color:rgba(0, 0, 0, 0);outline: none;}
input[type=search]::-webkit-search-cancel-button{ -webkit-appearance: none; }
a:active, a:hover {outline: 0;}
a, a:visited{text-decoration:none;}
table {border-collapse: collapse;border-spacing: 0;}
table,table tr th, table tr td { border:1px solid #0094ff; }
table{max-width: 100%;}
td,th {padding: 0;}
html,body{width:100%;height:100%;min-height:100%;overflow-y: scroll;}
img,video{max-width: 100%;display:block;max-height: auto;}
*{word-break: break-all;font-family: inherit;font-size: 0.16rem;outline: none;resize:none;-webkit-appearance: none;appearance: none;box-sizing: border-box;}
.click:active{background: rgba(0,0,0,0.03)}
#app{position: relative;}
.van-pull-refresh__track{height: 100%;}
.pull_box{height: 100%;overflow-y: auto;}


/* vant字体相关 */
#app .van-cell{padding: 0.1rem 0.16rem;line-height: normal;font-size: inherit;font-family: inherit;color: #222;}
#app .van-cell .van-cell__title{font-size: inherit;font-family: inherit;}
#app .van-cell .van-cell__title span{font-size: inherit;font-family: inherit;}
#app .van-dropdown-menu{height: auto;font-size: inherit;font-family: inherit;}
#app .van-dropdown-menu .van-dropdown-menu__title{line-height:1.4;height: auto;font-size: inherit;font-family: inherit;}
#app .van-dropdown-menu__title::after{border: 0.03rem solid;border-color: transparent transparent #323232 #323232;}
#app .van-dropdown-menu__title--active::after {border-color: transparent transparent currentColor currentColor;}
#app .search_warp .van-dropdown-menu__bar{height:100%;}
#app .van-dropdown-menu div{font-size: inherit;font-family: inherit;}
#app .van-dropdown-item,#app .van-dropdown-item div{font-size: inherit;font-family: inherit;}

#app .van-swipe-cell__right{font-size: inherit;font-family: inherit;}
#app .van-swipe-cell__right .van-button{height:100%;}
#app .van-tabs{height: auto;font-size: inherit;font-family: inherit;}
#app .van-tabs__wrap{height: auto;font-size: inherit;font-family: inherit;}
#app .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after{border-width: 0px 0;}
#app .van-tabs__nav,#app .van-tab,#app .van-tab__text{font-size: inherit;font-family: inherit;}
#app .van-tag{font-size: inherit;font-family: inherit;line-height: inherit;border-radius:0.02rem;padding: 0 0.04rem;}
#app .van-info{}
#app .van-tabs__line{bottom: 0.03rem;}
#app .van-tab{padding: 0.09rem 0.07rem 0.05rem 0.09rem;min-width: auto;}
#app .header .van-tab{padding: 0.09rem 0.36rem 0.05rem 0.36rem;}
#app .van-button__content{font-size: inherit;font-family: inherit;}
#app .van-button,#app .van-button .van-button__text{font-size: inherit;font-family: inherit;}
#app .van-button{height: auto;line-height: 1.2;padding: 0.1rem 0.12rem;}
#app .setting_item .van-dropdown-item .van-popup{padding-top: 0.1rem;}
#app .setting_item .van-dropdown-item .van-button{border-radius: 0;}
.van-skeleton+.van-skeleton{margin-top: 0.2rem;}
#app .van-uploader__preview{margin: 0 0.08rem 0.08rem 0;position: relative;}
.select_img_state{position: absolute;bottom: 0;right: -0.04rem;}
#app .van-uploader__preview-image{width: 0.77rem;height: 0.77rem;}
#app .van-cell__value{margin: auto;}
#app .van-field__left-icon{font-size: inherit;font-family: inherit;margin-right: 0.05rem;display:-webkit-box;display: -moz-box;display: -ms-flexbox;display: -webkit-flex;display: flex;align-items:center;-webkit-align-items:center;box-align:center;-moz-box-align:center;-webkit-box-align:center;}
#app .van-field__left-icon .van-icon{font-size: inherit;}
#app .van-field__clear{font-size: inherit;}
#app .van-search{font-size: inherit;font-family: inherit;padding: 0.1rem 0.12rem;}
#app .van-search__content{font-size: inherit;font-family: inherit;padding-left: 0;}
#app .van-cell__value,#app .van-cell__value span,#app .van-field__body,#app .van-switch__node{font-size: inherit;font-family: inherit;}
#app .van-tabs__nav--line {padding-bottom: 0.07rem;}
#app .van-uploader__upload{width: 0.8rem;height: 0.8rem;margin: 0 0.08rem 0.08rem 0;border-radius: 0.04rem;}
#app .van-uploader__preview-delete{font-size: 0.2rem;top: -0.05rem;right: -0.02rem;background-color: inherit;}
#app .van-dialog__header,#app .van-dialog__content,#app .van-dialog__footer{font-size: inherit;font-family: inherit;}
#app .van-dialog__header{padding-top: 0.25rem;line-height: 1.5;}
#app .van-dialog__message--has-title{padding-top: 0.1rem;}
#app .van-dialog__header--isolated{padding: 0.21rem 0;line-height: 1.4;font-weight: bold;color:#333}
#app .van-dialog{width:2.88rem;font-size: inherit;font-family: inherit;}
#app .van-password-input{font-size: inherit;font-family: inherit;margin: 0 0.16rem;}
#app .van-password-input__security{font-size: inherit;font-family: inherit;height: 0.5rem}
#app .van-password-input__item{font-size: 150%;font-family: inherit;border:1px solid #e1e1e1;}
#app .van-dialog__cancel, #app .van-dialog__confirm{padding: 0.16rem 0;}
body .van-toast{padding: 0.08rem 0.12rem;}
#app .van-radio-group,#app .van-radio,#app .van-checkbox-group,#app .van-checkbox{font-size: inherit;font-family: inherit;}
#app .van-checkbox__icon{font-size: inherit;font-family: inherit;}
#app .van-radio__label,#app .van-checkbox__label{line-height:1.4;margin-left: 0.04rem;font-size: inherit;font-family: inherit;}
body .van-popup__close-iconfont--top-right,#app .van-calendar__popup .van-popup__close-icon{top: 0.15rem;right: 0.15rem;}
body .van-popup{font-size: inherit;font-family: inherit;}
body .van-popup__close-icon{font-size: inherit;}
#app .van-count-down{line-height:inherit;font-size: inherit;font-family: inherit;color:inherit; }
#app .van-grid-item__content,#app .van-grid-item__text{font-size: inherit;font-family: inherit;color: #434444;text-align: center;}
#app .van-grid-item__content{padding: 0.15rem 0.07rem;}
#app .van-grid-item__text{margin-top: 0.03rem;}
#app .van-grid-item__content::after{border-width: 0;}
#app .van-share-sheet__cancel{font-size: inherit;font-family: inherit;line-height:2.7;}
#app .van-share-sheet__header,#app .van-share-sheet__title{font-size: inherit;font-family: inherit;}
#app .van-share-sheet__options,#app .van-share-sheet__option{font-size: inherit;font-family: inherit;}
#app .van-share-sheet__icon{width: 0.48rem;height: 0.48rem;}
#app .van-share-sheet__name{font-family: inherit;font-size: 85%;}
#app .van-dropdown-menu__bar{height: inherit;background: rgba(0,0,0,0);box-shadow:none;}
#app .van-dropdown-item .van-popup .van-cell .van-cell__value{-webkit-box-flex: none;-webkit-flex: none;flex: none;margin-left: 0.15rem;display: -webkit-box;display: -moz-box;display: -ms-flexbox;display: -webkit-flex;display: flex;align-items: center;-webkit-align-items: center;box-align: center;-moz-box-align: center;-webkit-box-align: center;-webkit-justify-content: center;justify-content: center;-moz-box-pack: center;-webkit--moz-box-pack: center;box-pack: center;}
#app .van-dropdown-menu .van-ellipsis{white-space:normal;text-overflow:clip;-webkit-line-clamp:1;-webkit-box-orient: vertical;display: -webkit-box;overflow: hidden;}
#app .van-sidebar{font-size: inherit;font-family: inherit;width: 1.1rem;}
#app .van-sidebar-item{font-size: inherit;font-family: inherit;padding: 0.2rem 0.12rem;}
#app .van-sidebar-item__text{font-size: inherit;font-family: inherit;}
#app .van-sidebar-item--select::before{width: 0.04rem;height: 0.16rem;}
#app .van-empty__image {width: 2rem;height: 1.08rem;}
#app .van-empty__image img{object-fit: cover;}
#app .custom-image .van-empty__image {width: 0.9rem;height: 0.9rem;}
#app .van-empty{padding: 0.32rem 0;font-size: inherit;font-family: inherit;}
#app .van-empty__description{font-size: inherit;font-family: inherit;margin-top: 0.42rem;line-height: 1;text-align: center}
.van-empty__description_text{font-weight: 600;color: #666666;}
.van-empty__description_summary{font-weight: 600;color: #999999;margin-top: 0.1rem;}
#app .van-empty__bottom{margin-top: 0.3rem;}
#app .van-empty__bottom .van-button{padding: 0.05rem 0.49rem;}
#app .van-cell-group{font-size: inherit;font-family: inherit;}
#app .van-divider{font-size: inherit;font-family: inherit;}
#app .van-cell__left-icon,#app .van-cell__right-icon{font-size: inherit;color:#B2B2B2;}
body .van-dialog{border-radius: 0.1rem;}
body .van-dialog__message{font-size: inherit;font-family: inherit;padding: 0.26rem 0.24rem;line-height: 1.5;}
#app .van-swipe-cell,#app .van-swipe-cell__wrapper{font-size: inherit;font-family: inherit;}
#app .van-sticky{font-size: inherit;font-family: inherit;}
#app .van-number-keyboard,#app .van-number-keyboard__header,#app .van-number-keyboard__title,#app .van-number-keyboard__close{font-size: inherit;font-family: inherit;}
#app .van-number-keyboard__body,#app .van-number-keyboard__keys{font-size: inherit;font-family: inherit;}
#app .van-key__wrapper{font-size: inherit;font-family: inherit;padding: 0 0.06rem 0.06rem 0;}
#app .van-key{border-radius: 0.08rem;height: 0.5rem;font-size: 0.27rem;}
#app .van-picker,#app .van-picker__toolbar,#app .van-picker__cancel,#app .van-picker__title,#app .van-picker__confirm{font-size: inherit;font-family: inherit;}
#app .van-picker__toolbar{min-height: 0.44rem;padding: 0.1rem 0;}
#app .van-picker__title{line-height: inherit;}
#app .van-picker__columns,#app .van-picker-column,#app .van-picker-column__wrapper,#app .van-picker-column__item,#app .van-picker-column__item .van-ellipsis{font-size: inherit;font-family: inherit;}
#app .van-field__label{margin-right: 0.1rem;color: #323233;}
#app .van-calendar,#app .van-calendar__header,#app .van-calendar__body,#app .van-calendar__footer,#app .van-calendar__header-title{font-size: inherit;font-family: inherit;}
#app .van-calendar__header-subtitle,#app .van-calendar__month-title{font-size: 0.14rem;}
#app .van-calendar__weekday{font-size: 0.12rem;}
#app .van-calendar__day{font-size: 0.16rem;}
#app .van-calendar__bottom-info{font-size: 0.11rem;}
#app .van-sidebar-item{line-height: 1.4;}
#app .van-tree-select__item{padding: 0 0.3rem 0 0.15rem;line-height: 2.5;}
#app .van-tree-select__content,#app .van-tree-select__item{font-size: inherit;font-family: inherit;}
#app .van-tree-select__selected{font-size: inherit;right: 0.15rem;top:43%;}
#app .van-progress,#app .van-progress__portion,#app .van-progress__pivot{font-size: inherit;font-family: inherit;}
#app .van-info{min-width: 0.16rem;padding: 0 0.03rem;font-size: 0.12rem;border-radius: 0.16rem;}
#app .van-index-bar__sidebar{z-index: 100;}
#app .van-index-bar__index{padding: 0 0.04rem 0 0.16rem;font-size: 0.14rem;line-height: 1.4;font-weight: bold;}
#app .van-index-anchor{padding: 0 0.16rem;color: #333;font-weight: bold;font-size: 0.16rem;line-height: 1.9;}
#app .van-collapse-item__content{padding: 0;}
#app .van-collapse-item{font-size: inherit;font-family: inherit;color: inherit;}
#app .van-collapse-item__title{padding: 0.15rem 0.14rem;align-items:center;-webkit-align-items:center;box-align:center;-moz-box-align:center;-webkit-box-align:center;}
#app .van-collapse-item__title .van-cell__title{font-weight: 600;color: #333;}
#app .van-collapse-item__title .van-cell__right-icon{color: #7F7F7F;}
#app .van-collapse-item__title--expanded{color: inherit;}
#app .van-collapse-item__title--expanded .van-cell__title{font-weight: bold;color: inherit;}
#app .van-collapse-item__title .van-cell__right-icon{color: inherit;}
#app .van-collapse-item__title .van-cell__right-icon::before{-webkit-transform: rotate(0deg);transform: rotate(0deg);-webkit-transition: -webkit-transform .3s;transition: -webkit-transform .3s;transition: transform .3s;transition: transform .3s,-webkit-transform .3s;}
#app .van-collapse-item__title--expanded .van-cell__right-icon::before{-webkit-transform: rotate(90deg);transform: rotate(90deg);-webkit-transition: -webkit-transform .3s;transition: -webkit-transform .3s;transition: transform .3s;transition: transform .3s,-webkit-transform .3s;}
#app .van-popup--bottom.van-popup--round{border-radius: 0.16rem 0.16rem 0 0;}
#app .van-picker-column__item--selected,#app .van-picker__confirm{color:#0271E3;}
#app .van-action-sheet__content,#app .van-action-sheet__item,#app .van-action-sheet__name{font-size: inherit;font-family: inherit;}
#app .van-action-sheet__gap{height: 0.08rem;}
#app .van-action-sheet__item{line-height: 1.4;}
#app .van-action-sheet__cancel{font-size: inherit;font-family: inherit;}
#app .van-action-sheet__header{font-size: 90%;font-family: inherit;line-height:2.8;}
#app .van-popover__wrapper,#app .van-popover__content,#app .van-popover__action,#app .van-popover__action-text{font-size: inherit;font-family: inherit;}
#app .van-popover__action{line-height:1.4;width: auto;height: auto;padding: 0.1rem 0.16rem;}
#app .van-popover__action-text::after{border-bottom-width:0px;}

/* 其它定义 */
.attach_warp .cache span {color: blue;}
/* 这个class承接上级样式 */
#app .inherit{font-size: inherit;font-family: inherit;}

/****************************************************************/
.notText{width: 100%;text-align: center;padding: 0.15rem 0;background: rgba(0,0,0,0);color: #CCCCCC;}
.notText img{width: 60%;margin: 0.2rem 20%;}


.none{display: none;}
.text_one{text-overflow: ellipsis;white-space: nowrap;overflow: hidden;}
.text_one2{-webkit-line-clamp:1;-webkit-box-orient: vertical;display: -webkit-box;overflow: hidden;}
.text_two{-webkit-line-clamp: 2;-webkit-box-orient: vertical;display: -webkit-box;overflow: hidden;}
.line05{position: relative;}
.line05:after{display:block;content:'';width:200%;height:1px;background-color:#dedede;transform: scale(0.5);-webkit-transform: scale(0.5);-webkit-transform-origin:0 0;box-sizing:border-box;}
.tapmode{background:rgba(0,0,0,0.2)}

/* 2019年1月12日 10:14:06*兼容写法**************************************************************/
/*定义flex布局    */
.flex_box{display:-webkit-box;display: -moz-box;display: -ms-flexbox;display: -webkit-flex;display: flex;}
/*flex占位    */
.flex_placeholder{box-flex:1;-webkit-box-flex:1;-moz-box-flex:1;flex: 1;-webkit-flex:1;}
/*flex排列方向 上下    */
.flex_flex_direction_column{-webkit-box-orient:vertical;-webkit-box-direction:normal;-moz-box-orient:vertical;-moz-box-direction:normal;flex-direction:column;-webkit-flex-direction:column;}
/*flex上下居中    */
.flex_align_center{align-items:center;-webkit-align-items:center;box-align:center;-moz-box-align:center;-webkit-box-align:center;}
/*flex上下居上    */
.flex_align_start{align-items:flex-start;-webkit-align-items:flex-start;box-align:flex-start;-moz-box-align:flex-start;-webkit-box-align:flex-start;}
/*flex上下居下    */
.flex_align_end{align-items:flex-end;-webkit-align-items:flex-end;box-align:flex-end;-moz-box-align:flex-end;-webkit-box-align:flex-end;}

/*flex左右居中    */
.flex_justify_content{-webkit-justify-content:center;justify-content:center;-moz-box-pack:center;-webkit--moz-box-pack:center;box-pack:center;}
/* 左右 居左 */
.flex_justify-content_start{-webkit-justify-content:flex-start;justify-content:flex-start;-moz-box-pack:flex-start;-webkit--moz-box-pack:flex-start;box-pack:flex-start;}
/* 左右 居右 */
.flex_justify-content_end{-webkit-justify-content:flex-end;justify-content:flex-end;-moz-box-pack:flex-end;-webkit--moz-box-pack:flex-end;box-pack:flex-end;}

.T-iframe{position: absolute;top: 0;right: 0;bottom: 0;left: 0;width: 100%;height: 100%;z-index: 100;}
/* 常用流布局  vertical垂直布局 horizontal水平 item占满    */
.T-flexbox-vertical { display: box; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-flex-flow: column; flex-flow: column; height: 100%; -webkit-flex-direction: column; flex-direction: column; box-sizing: border-box; -webkit-box-sizing: border-box; }
.T-flexbox-horizontal { display: box; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: horizontal; -webkit-flex-flow: row; flex-flow: row; width: 100%; -webkit-flex-direction: row; flex-direction: row; box-sizing: border-box; -webkit-box-sizing: border-box; }
.T-flex-item { -webkit-box-flex: 1; -webkit-flex: 1; flex: 1; box-sizing: border-box; -webkit-box-sizing: border-box; }
/* 占满了换行  下面的应该是不换行 */
.T-flex-flow-row-wrap { flex-direction: row; flex-wrap: wrap; -webkit-flex-direction: row; -webkit-flex-wrap: wrap; }
.T-flex-flow-row-nowrap { flex-direction: row; flex-wrap: nowrap; -webkit-flex-direction: row; -webkit-flex-wrap: nowrap; }

/* 右下角按钮样式  具体参考demo_page */
.footer_btn_box{position: fixed;bottom: 0.58rem;right: 0.1rem;-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;z-index: 9;}
.footer_btn_box .footer_item{float:right;}
.footer_btn_box .van-button-box{margin-bottom: 0.1rem;}
#app .footer_btn_box .van-button{border-radius: 0.05rem;width: 100%;padding: 0.09rem 0.13rem;}
.footer_btn_box .back_top{z-index: 9;margin: 0 auto;margin-bottom: 0.08rem;text-align: center;}
.footer_btn_box .back_top .van-iconfont-upgrade{background: #FFF;border-radius: 50%;color: #333;}
.footer_nBtn_box{box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);border-radius: 50%;}

/*内容的样式 处理内容的样式*/
.n_details_content{width: 100%;padding: 0.14rem;box-sizing: border-box;color: #333333;line-height: 1.8;}
.n_details_content *{font-size: inherit;font-family: inherit;}
/* 通用说明提示样式    */
.hint_default{background: #FFF;padding: 0.15rem 0.14rem;}
.hint_default_icon{width:0.05rem;margin-right: 0.08rem;}
/*自定义搜索**************************************************************/
.search_box{padding: 0.09rem 0.15rem;box-sizing: border-box;background: #fff;}
.search_box .search_warp{height: 100%;background: #F8F8F8;border-radius: 0.04rem;padding-left: 0.04rem;}
.search_box .search_warp{ padding: 0.06rem 0; }
.search_box form{width:1px;}
.search_box input{width:1px;margin: auto;line-height: 1.4;}
.search_box input::-webkit-input-placeholder{ color:#999; }
.search_box input::-moz-placeholder{ color:#999; }
.search_box input:-moz-placeholder{ color:#999; }
.search_box input:-ms-input-placeholder{ color:#999; }
.search_box .search_btn{padding: 0 0.1rem;}
.search_box .search_btn + form{ padding-left:0rem; }
.search_box .search-dropdown-menu{margin-right:0.15rem;}

/*展开收起 **************************************************************/
.hide_preCode_box{width: 100%;background-image: -webkit-gradient(linear,left top, left bottom,from(rgba(255,255,255,0)),to(#fff));background-image: linear-gradient(-180deg,rgba(255,255,255,0) 0%,#fff 100%);left: 0;right: 0;bottom: 0;z-index: 10;}
.arrowOpen{-webkit-transform: rotate(90deg);transform: rotate(90deg);-webkit-transition: -webkit-transform 0.3s;transition: -webkit-transform 0.3s;transition: transform 0.3s;transition: transform 0.3s, -webkit-transform 0.3s;}
.arrowClose{-webkit-transform: rotate(-90deg);transform: rotate(-90deg);-webkit-transition: -webkit-transform 0.3s;transition: -webkit-transform 0.3s;transition: transform 0.3s;transition: transform 0.3s, -webkit-transform 0.3s;}
