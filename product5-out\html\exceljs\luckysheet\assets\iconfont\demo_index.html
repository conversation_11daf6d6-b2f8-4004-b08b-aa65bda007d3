<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1990368" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f8;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe7f8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f5;</span>
                <div class="name">打印区域</div>
                <div class="code-name">&amp;#xe7f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f6;</span>
                <div class="name">打印页面配置</div>
                <div class="code-name">&amp;#xe7f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f7;</span>
                <div class="name">打印标题</div>
                <div class="code-name">&amp;#xe7f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f2;</span>
                <div class="name">分页预览</div>
                <div class="code-name">&amp;#xe7f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f3;</span>
                <div class="name">普通</div>
                <div class="code-name">&amp;#xe7f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f4;</span>
                <div class="name">页面布局</div>
                <div class="code-name">&amp;#xe7f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ee;</span>
                <div class="name">表格锁定</div>
                <div class="code-name">&amp;#xe7ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f1;</span>
                <div class="name">转到</div>
                <div class="code-name">&amp;#xe7f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ed;</span>
                <div class="name">右箭头</div>
                <div class="code-name">&amp;#xe7ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ef;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xe7ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f0;</span>
                <div class="name">替换</div>
                <div class="code-name">&amp;#xe7f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e1;</span>
                <div class="name">冻结</div>
                <div class="code-name">&amp;#xe7e1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e2;</span>
                <div class="name">剪</div>
                <div class="code-name">&amp;#xe7e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e3;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe7e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e4;</span>
                <div class="name">溢出</div>
                <div class="code-name">&amp;#xe7e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e5;</span>
                <div class="name">升序</div>
                <div class="code-name">&amp;#xe7e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e6;</span>
                <div class="name">内框线</div>
                <div class="code-name">&amp;#xe7e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e7;</span>
                <div class="name">清除筛选</div>
                <div class="code-name">&amp;#xe7e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e8;</span>
                <div class="name">文本向上</div>
                <div class="code-name">&amp;#xe7e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e9;</span>
                <div class="name">降序</div>
                <div class="code-name">&amp;#xe7e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ea;</span>
                <div class="name">内框横线</div>
                <div class="code-name">&amp;#xe7ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7eb;</span>
                <div class="name">内框竖线</div>
                <div class="code-name">&amp;#xe7eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ec;</span>
                <div class="name">自定义排序</div>
                <div class="code-name">&amp;#xe7ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7df;</span>
                <div class="name">logo2</div>
                <div class="code-name">&amp;#xe7df;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e0;</span>
                <div class="name">logo</div>
                <div class="code-name">&amp;#xe7e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7de;</span>
                <div class="name">文本倾斜</div>
                <div class="code-name">&amp;#xe7de;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d9;</span>
                <div class="name">加粗</div>
                <div class="code-name">&amp;#xe7d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78a;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe78a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78b;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe78b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78c;</span>
                <div class="name">下一个</div>
                <div class="code-name">&amp;#xe78c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78d;</span>
                <div class="name">下拉</div>
                <div class="code-name">&amp;#xe78d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78e;</span>
                <div class="name">文本颜色</div>
                <div class="code-name">&amp;#xe78e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78f;</span>
                <div class="name">上一个</div>
                <div class="code-name">&amp;#xe78f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe790;</span>
                <div class="name">数据透视</div>
                <div class="code-name">&amp;#xe790;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe791;</span>
                <div class="name">填充</div>
                <div class="code-name">&amp;#xe791;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe792;</span>
                <div class="name">增加小数位</div>
                <div class="code-name">&amp;#xe792;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe793;</span>
                <div class="name">编辑2</div>
                <div class="code-name">&amp;#xe793;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe794;</span>
                <div class="name">截屏</div>
                <div class="code-name">&amp;#xe794;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe796;</span>
                <div class="name">减小小数位</div>
                <div class="code-name">&amp;#xe796;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe797;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xe797;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe798;</span>
                <div class="name">数据库</div>
                <div class="code-name">&amp;#xe798;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe799;</span>
                <div class="name">无边框</div>
                <div class="code-name">&amp;#xe799;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79a;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe79a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79b;</span>
                <div class="name">清除样式</div>
                <div class="code-name">&amp;#xe79b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79c;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe79c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79d;</span>
                <div class="name">文本居中对齐</div>
                <div class="code-name">&amp;#xe79d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79e;</span>
                <div class="name">打印</div>
                <div class="code-name">&amp;#xe79e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79f;</span>
                <div class="name">文本分割</div>
                <div class="code-name">&amp;#xe79f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a0;</span>
                <div class="name">函数‘</div>
                <div class="code-name">&amp;#xe7a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a1;</span>
                <div class="name">降序</div>
                <div class="code-name">&amp;#xe7a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a2;</span>
                <div class="name">顶部对齐</div>
                <div class="code-name">&amp;#xe7a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a3;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe7a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a4;</span>
                <div class="name">向下90</div>
                <div class="code-name">&amp;#xe7a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a5;</span>
                <div class="name">竖排文字</div>
                <div class="code-name">&amp;#xe7a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a6;</span>
                <div class="name">全加边框</div>
                <div class="code-name">&amp;#xe7a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a7;</span>
                <div class="name">升序</div>
                <div class="code-name">&amp;#xe7a7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a8;</span>
                <div class="name">裁剪</div>
                <div class="code-name">&amp;#xe7a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a9;</span>
                <div class="name">金额</div>
                <div class="code-name">&amp;#xe7a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7aa;</span>
                <div class="name">菜单1</div>
                <div class="code-name">&amp;#xe7aa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ab;</span>
                <div class="name">取消合并</div>
                <div class="code-name">&amp;#xe7ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ac;</span>
                <div class="name">文本下划线</div>
                <div class="code-name">&amp;#xe7ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ad;</span>
                <div class="name">上边框</div>
                <div class="code-name">&amp;#xe7ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ae;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7af;</span>
                <div class="name">四周加边框</div>
                <div class="code-name">&amp;#xe7af;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b0;</span>
                <div class="name">侧边栏收起</div>
                <div class="code-name">&amp;#xe7b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b1;</span>
                <div class="name">合并</div>
                <div class="code-name">&amp;#xe7b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b2;</span>
                <div class="name">向上倾斜</div>
                <div class="code-name">&amp;#xe7b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b3;</span>
                <div class="name">水平对齐</div>
                <div class="code-name">&amp;#xe7b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b4;</span>
                <div class="name">文本删除线</div>
                <div class="code-name">&amp;#xe7b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b5;</span>
                <div class="name">文本右对齐</div>
                <div class="code-name">&amp;#xe7b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b6;</span>
                <div class="name">前进</div>
                <div class="code-name">&amp;#xe7b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b7;</span>
                <div class="name">图表</div>
                <div class="code-name">&amp;#xe7b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b8;</span>
                <div class="name">右边框</div>
                <div class="code-name">&amp;#xe7b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b9;</span>
                <div class="name">百分号</div>
                <div class="code-name">&amp;#xe7b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ba;</span>
                <div class="name">格式刷</div>
                <div class="code-name">&amp;#xe7ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bb;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe7bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bc;</span>
                <div class="name">数据验证</div>
                <div class="code-name">&amp;#xe7bc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bd;</span>
                <div class="name">截断</div>
                <div class="code-name">&amp;#xe7bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7be;</span>
                <div class="name">格式条件</div>
                <div class="code-name">&amp;#xe7be;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bf;</span>
                <div class="name">自动换行</div>
                <div class="code-name">&amp;#xe7bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c0;</span>
                <div class="name">侧边栏展开</div>
                <div class="code-name">&amp;#xe7c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c1;</span>
                <div class="name">筛选2</div>
                <div class="code-name">&amp;#xe7c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c2;</span>
                <div class="name">向下倾斜</div>
                <div class="code-name">&amp;#xe7c2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c3;</span>
                <div class="name">溢出</div>
                <div class="code-name">&amp;#xe7c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c4;</span>
                <div class="name">垂直合并</div>
                <div class="code-name">&amp;#xe7c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c5;</span>
                <div class="name">文本分散对齐</div>
                <div class="code-name">&amp;#xe7c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c6;</span>
                <div class="name">左边框</div>
                <div class="code-name">&amp;#xe7c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c7;</span>
                <div class="name">分页查看</div>
                <div class="code-name">&amp;#xe7c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c8;</span>
                <div class="name">运行</div>
                <div class="code-name">&amp;#xe7c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c9;</span>
                <div class="name">列</div>
                <div class="code-name">&amp;#xe7c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ca;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe7ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cb;</span>
                <div class="name">筛选</div>
                <div class="code-name">&amp;#xe7cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cc;</span>
                <div class="name">更新</div>
                <div class="code-name">&amp;#xe7cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cd;</span>
                <div class="name">清除</div>
                <div class="code-name">&amp;#xe7cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ce;</span>
                <div class="name">行</div>
                <div class="code-name">&amp;#xe7ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cf;</span>
                <div class="name">注释</div>
                <div class="code-name">&amp;#xe7cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d0;</span>
                <div class="name">剪</div>
                <div class="code-name">&amp;#xe7d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d1;</span>
                <div class="name">计算</div>
                <div class="code-name">&amp;#xe7d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d2;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe7d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d3;</span>
                <div class="name">底部对齐</div>
                <div class="code-name">&amp;#xe7d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d4;</span>
                <div class="name">向上90</div>
                <div class="code-name">&amp;#xe7d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d5;</span>
                <div class="name">无选装</div>
                <div class="code-name">&amp;#xe7d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d6;</span>
                <div class="name">显示隐藏网格</div>
                <div class="code-name">&amp;#xe7d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d7;</span>
                <div class="name">冻结</div>
                <div class="code-name">&amp;#xe7d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d8;</span>
                <div class="name">文本左对齐</div>
                <div class="code-name">&amp;#xe7d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7da;</span>
                <div class="name">后退</div>
                <div class="code-name">&amp;#xe7da;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7db;</span>
                <div class="name">水平合并</div>
                <div class="code-name">&amp;#xe7db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dc;</span>
                <div class="name">下边框</div>
                <div class="code-name">&amp;#xe7dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dd;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xe7dd;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-lianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.icon-lianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dayinquyu"></span>
            <div class="name">
              打印区域
            </div>
            <div class="code-name">.icon-dayinquyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dayinyemianpeizhi"></span>
            <div class="name">
              打印页面配置
            </div>
            <div class="code-name">.icon-dayinyemianpeizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dayinbiaoti"></span>
            <div class="name">
              打印标题
            </div>
            <div class="code-name">.icon-dayinbiaoti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenyeyulan"></span>
            <div class="name">
              分页预览
            </div>
            <div class="code-name">.icon-fenyeyulan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-putong"></span>
            <div class="name">
              普通
            </div>
            <div class="code-name">.icon-putong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yemianbuju"></span>
            <div class="name">
              页面布局
            </div>
            <div class="code-name">.icon-yemianbuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaogesuoding"></span>
            <div class="name">
              表格锁定
            </div>
            <div class="code-name">.icon-biaogesuoding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhuandao1"></span>
            <div class="name">
              转到
            </div>
            <div class="code-name">.icon-zhuandao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youjiantou"></span>
            <div class="name">
              右箭头
            </div>
            <div class="code-name">.icon-youjiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caidan2"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.icon-caidan2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tihuan"></span>
            <div class="name">
              替换
            </div>
            <div class="code-name">.icon-tihuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dongjie1"></span>
            <div class="name">
              冻结
            </div>
            <div class="code-name">.icon-dongjie1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jian1"></span>
            <div class="name">
              剪
            </div>
            <div class="code-name">.icon-jian1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jia1"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.icon-jia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yichu1"></span>
            <div class="name">
              溢出
            </div>
            <div class="code-name">.icon-yichu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shengxu1"></span>
            <div class="name">
              升序
            </div>
            <div class="code-name">.icon-shengxu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-neikuangxian"></span>
            <div class="name">
              内框线
            </div>
            <div class="code-name">.icon-neikuangxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingchushaixuan"></span>
            <div class="name">
              清除筛选
            </div>
            <div class="code-name">.icon-qingchushaixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenxiangshang"></span>
            <div class="name">
              文本向上
            </div>
            <div class="code-name">.icon-wenbenxiangshang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiangxu1"></span>
            <div class="name">
              降序
            </div>
            <div class="code-name">.icon-jiangxu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-neikuanghengxian"></span>
            <div class="name">
              内框横线
            </div>
            <div class="code-name">.icon-neikuanghengxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-neikuangshuxian"></span>
            <div class="name">
              内框竖线
            </div>
            <div class="code-name">.icon-neikuangshuxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidingyipaixu"></span>
            <div class="name">
              自定义排序
            </div>
            <div class="code-name">.icon-zidingyipaixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-logo2"></span>
            <div class="name">
              logo2
            </div>
            <div class="code-name">.icon-logo2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-logo"></span>
            <div class="name">
              logo
            </div>
            <div class="code-name">.icon-logo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenqingxie1"></span>
            <div class="name">
              文本倾斜
            </div>
            <div class="code-name">.icon-wenbenqingxie1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiacu"></span>
            <div class="name">
              加粗
            </div>
            <div class="code-name">.icon-jiacu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.icon-guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiayige"></span>
            <div class="name">
              下一个
            </div>
            <div class="code-name">.icon-xiayige
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiala"></span>
            <div class="name">
              下拉
            </div>
            <div class="code-name">.icon-xiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenyanse"></span>
            <div class="name">
              文本颜色
            </div>
            <div class="code-name">.icon-wenbenyanse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangyige"></span>
            <div class="name">
              上一个
            </div>
            <div class="code-name">.icon-shangyige
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shujutoushi"></span>
            <div class="name">
              数据透视
            </div>
            <div class="code-name">.icon-shujutoushi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianchong"></span>
            <div class="name">
              填充
            </div>
            <div class="code-name">.icon-tianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zengjiaxiaoshuwei"></span>
            <div class="name">
              增加小数位
            </div>
            <div class="code-name">.icon-zengjiaxiaoshuwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji2"></span>
            <div class="name">
              编辑2
            </div>
            <div class="code-name">.icon-bianji2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jieping"></span>
            <div class="name">
              截屏
            </div>
            <div class="code-name">.icon-jieping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jianxiaoxiaoshuwei"></span>
            <div class="name">
              减小小数位
            </div>
            <div class="code-name">.icon-jianxiaoxiaoshuwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caidan"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.icon-caidan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shujuku"></span>
            <div class="name">
              数据库
            </div>
            <div class="code-name">.icon-shujuku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wubiankuang"></span>
            <div class="name">
              无边框
            </div>
            <div class="code-name">.icon-wubiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingchuyangshi"></span>
            <div class="name">
              清除样式
            </div>
            <div class="code-name">.icon-qingchuyangshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenjuzhongduiqi"></span>
            <div class="name">
              文本居中对齐
            </div>
            <div class="code-name">.icon-wenbenjuzhongduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dayin"></span>
            <div class="name">
              打印
            </div>
            <div class="code-name">.icon-dayin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenfenge"></span>
            <div class="name">
              文本分割
            </div>
            <div class="code-name">.icon-wenbenfenge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hanshu"></span>
            <div class="name">
              函数‘
            </div>
            <div class="code-name">.icon-hanshu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiangxu"></span>
            <div class="name">
              降序
            </div>
            <div class="code-name">.icon-jiangxu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingbuduiqi"></span>
            <div class="name">
              顶部对齐
            </div>
            <div class="code-name">.icon-dingbuduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.icon-tupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangxia90"></span>
            <div class="name">
              向下90
            </div>
            <div class="code-name">.icon-xiangxia90
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shupaiwenzi"></span>
            <div class="name">
              竖排文字
            </div>
            <div class="code-name">.icon-shupaiwenzi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanjiabiankuang"></span>
            <div class="name">
              全加边框
            </div>
            <div class="code-name">.icon-quanjiabiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shengxu"></span>
            <div class="name">
              升序
            </div>
            <div class="code-name">.icon-shengxu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caijian"></span>
            <div class="name">
              裁剪
            </div>
            <div class="code-name">.icon-caijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jine"></span>
            <div class="name">
              金额
            </div>
            <div class="code-name">.icon-jine
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caidan1"></span>
            <div class="name">
              菜单1
            </div>
            <div class="code-name">.icon-caidan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quxiaohebing"></span>
            <div class="name">
              取消合并
            </div>
            <div class="code-name">.icon-quxiaohebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenxiahuaxian"></span>
            <div class="name">
              文本下划线
            </div>
            <div class="code-name">.icon-wenbenxiahuaxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangbiankuang"></span>
            <div class="name">
              上边框
            </div>
            <div class="code-name">.icon-shangbiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingwei"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.icon-dingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sizhoujiabiankuang"></span>
            <div class="name">
              四周加边框
            </div>
            <div class="code-name">.icon-sizhoujiabiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cebianlanshouqi"></span>
            <div class="name">
              侧边栏收起
            </div>
            <div class="code-name">.icon-cebianlanshouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hebing"></span>
            <div class="name">
              合并
            </div>
            <div class="code-name">.icon-hebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangshangqingxie"></span>
            <div class="name">
              向上倾斜
            </div>
            <div class="code-name">.icon-xiangshangqingxie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuipingduiqi"></span>
            <div class="name">
              水平对齐
            </div>
            <div class="code-name">.icon-shuipingduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenshanchuxian"></span>
            <div class="name">
              文本删除线
            </div>
            <div class="code-name">.icon-wenbenshanchuxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenyouduiqi"></span>
            <div class="name">
              文本右对齐
            </div>
            <div class="code-name">.icon-wenbenyouduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qianjin"></span>
            <div class="name">
              前进
            </div>
            <div class="code-name">.icon-qianjin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tubiao"></span>
            <div class="name">
              图表
            </div>
            <div class="code-name">.icon-tubiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youbiankuang"></span>
            <div class="name">
              右边框
            </div>
            <div class="code-name">.icon-youbiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baifenhao"></span>
            <div class="name">
              百分号
            </div>
            <div class="code-name">.icon-baifenhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-geshishua"></span>
            <div class="name">
              格式刷
            </div>
            <div class="code-name">.icon-geshishua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baocun"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.icon-baocun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shujuyanzheng"></span>
            <div class="name">
              数据验证
            </div>
            <div class="code-name">.icon-shujuyanzheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jieduan"></span>
            <div class="name">
              截断
            </div>
            <div class="code-name">.icon-jieduan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-geshitiaojian"></span>
            <div class="name">
              格式条件
            </div>
            <div class="code-name">.icon-geshitiaojian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zidonghuanhang"></span>
            <div class="name">
              自动换行
            </div>
            <div class="code-name">.icon-zidonghuanhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cebianlanzhankai"></span>
            <div class="name">
              侧边栏展开
            </div>
            <div class="code-name">.icon-cebianlanzhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shaixuan2"></span>
            <div class="name">
              筛选2
            </div>
            <div class="code-name">.icon-shaixuan2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangxiaqingxie"></span>
            <div class="name">
              向下倾斜
            </div>
            <div class="code-name">.icon-xiangxiaqingxie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yichu"></span>
            <div class="name">
              溢出
            </div>
            <div class="code-name">.icon-yichu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuizhihebing"></span>
            <div class="name">
              垂直合并
            </div>
            <div class="code-name">.icon-chuizhihebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenfensanduiqi"></span>
            <div class="name">
              文本分散对齐
            </div>
            <div class="code-name">.icon-wenbenfensanduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuobiankuang"></span>
            <div class="name">
              左边框
            </div>
            <div class="code-name">.icon-zuobiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenyechakan"></span>
            <div class="name">
              分页查看
            </div>
            <div class="code-name">.icon-fenyechakan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yunhang"></span>
            <div class="name">
              运行
            </div>
            <div class="code-name">.icon-yunhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lie"></span>
            <div class="name">
              列
            </div>
            <div class="code-name">.icon-lie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-quanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shaixuan"></span>
            <div class="name">
              筛选
            </div>
            <div class="code-name">.icon-shaixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengxin"></span>
            <div class="name">
              更新
            </div>
            <div class="code-name">.icon-gengxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingchu"></span>
            <div class="name">
              清除
            </div>
            <div class="code-name">.icon-qingchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-hang"></span>
            <div class="name">
              行
            </div>
            <div class="code-name">.icon-hang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhushi"></span>
            <div class="name">
              注释
            </div>
            <div class="code-name">.icon-zhushi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jian"></span>
            <div class="name">
              剪
            </div>
            <div class="code-name">.icon-jian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jisuan"></span>
            <div class="name">
              计算
            </div>
            <div class="code-name">.icon-jisuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jia"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.icon-jia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dibuduiqi"></span>
            <div class="name">
              底部对齐
            </div>
            <div class="code-name">.icon-dibuduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangshang90"></span>
            <div class="name">
              向上90
            </div>
            <div class="code-name">.icon-xiangshang90
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wuxuanzhuang"></span>
            <div class="name">
              无选装
            </div>
            <div class="code-name">.icon-wuxuanzhuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xianshiyincangwangge"></span>
            <div class="name">
              显示隐藏网格
            </div>
            <div class="code-name">.icon-xianshiyincangwangge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dongjie"></span>
            <div class="name">
              冻结
            </div>
            <div class="code-name">.icon-dongjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenzuoduiqi"></span>
            <div class="name">
              文本左对齐
            </div>
            <div class="code-name">.icon-wenbenzuoduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-houtui"></span>
            <div class="name">
              后退
            </div>
            <div class="code-name">.icon-houtui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuipinghebing"></span>
            <div class="name">
              水平合并
            </div>
            <div class="code-name">.icon-shuipinghebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiabiankuang"></span>
            <div class="name">
              下边框
            </div>
            <div class="code-name">.icon-xiabiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shezhi"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.icon-shezhi
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#icon-lianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayinquyu"></use>
                </svg>
                <div class="name">打印区域</div>
                <div class="code-name">#icon-dayinquyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayinyemianpeizhi"></use>
                </svg>
                <div class="name">打印页面配置</div>
                <div class="code-name">#icon-dayinyemianpeizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayinbiaoti"></use>
                </svg>
                <div class="name">打印标题</div>
                <div class="code-name">#icon-dayinbiaoti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenyeyulan"></use>
                </svg>
                <div class="name">分页预览</div>
                <div class="code-name">#icon-fenyeyulan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-putong"></use>
                </svg>
                <div class="name">普通</div>
                <div class="code-name">#icon-putong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yemianbuju"></use>
                </svg>
                <div class="name">页面布局</div>
                <div class="code-name">#icon-yemianbuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaogesuoding"></use>
                </svg>
                <div class="name">表格锁定</div>
                <div class="code-name">#icon-biaogesuoding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhuandao1"></use>
                </svg>
                <div class="name">转到</div>
                <div class="code-name">#icon-zhuandao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youjiantou"></use>
                </svg>
                <div class="name">右箭头</div>
                <div class="code-name">#icon-youjiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caidan2"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#icon-caidan2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tihuan"></use>
                </svg>
                <div class="name">替换</div>
                <div class="code-name">#icon-tihuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dongjie1"></use>
                </svg>
                <div class="name">冻结</div>
                <div class="code-name">#icon-dongjie1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jian1"></use>
                </svg>
                <div class="name">剪</div>
                <div class="code-name">#icon-jian1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jia1"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#icon-jia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yichu1"></use>
                </svg>
                <div class="name">溢出</div>
                <div class="code-name">#icon-yichu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shengxu1"></use>
                </svg>
                <div class="name">升序</div>
                <div class="code-name">#icon-shengxu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-neikuangxian"></use>
                </svg>
                <div class="name">内框线</div>
                <div class="code-name">#icon-neikuangxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingchushaixuan"></use>
                </svg>
                <div class="name">清除筛选</div>
                <div class="code-name">#icon-qingchushaixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenxiangshang"></use>
                </svg>
                <div class="name">文本向上</div>
                <div class="code-name">#icon-wenbenxiangshang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiangxu1"></use>
                </svg>
                <div class="name">降序</div>
                <div class="code-name">#icon-jiangxu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-neikuanghengxian"></use>
                </svg>
                <div class="name">内框横线</div>
                <div class="code-name">#icon-neikuanghengxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-neikuangshuxian"></use>
                </svg>
                <div class="name">内框竖线</div>
                <div class="code-name">#icon-neikuangshuxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidingyipaixu"></use>
                </svg>
                <div class="name">自定义排序</div>
                <div class="code-name">#icon-zidingyipaixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-logo2"></use>
                </svg>
                <div class="name">logo2</div>
                <div class="code-name">#icon-logo2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-logo"></use>
                </svg>
                <div class="name">logo</div>
                <div class="code-name">#icon-logo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenqingxie1"></use>
                </svg>
                <div class="name">文本倾斜</div>
                <div class="code-name">#icon-wenbenqingxie1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiacu"></use>
                </svg>
                <div class="name">加粗</div>
                <div class="code-name">#icon-jiacu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#icon-guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiayige"></use>
                </svg>
                <div class="name">下一个</div>
                <div class="code-name">#icon-xiayige</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiala"></use>
                </svg>
                <div class="name">下拉</div>
                <div class="code-name">#icon-xiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenyanse"></use>
                </svg>
                <div class="name">文本颜色</div>
                <div class="code-name">#icon-wenbenyanse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangyige"></use>
                </svg>
                <div class="name">上一个</div>
                <div class="code-name">#icon-shangyige</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujutoushi"></use>
                </svg>
                <div class="name">数据透视</div>
                <div class="code-name">#icon-shujutoushi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianchong"></use>
                </svg>
                <div class="name">填充</div>
                <div class="code-name">#icon-tianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zengjiaxiaoshuwei"></use>
                </svg>
                <div class="name">增加小数位</div>
                <div class="code-name">#icon-zengjiaxiaoshuwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji2"></use>
                </svg>
                <div class="name">编辑2</div>
                <div class="code-name">#icon-bianji2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jieping"></use>
                </svg>
                <div class="name">截屏</div>
                <div class="code-name">#icon-jieping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianxiaoxiaoshuwei"></use>
                </svg>
                <div class="name">减小小数位</div>
                <div class="code-name">#icon-jianxiaoxiaoshuwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caidan"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#icon-caidan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuku"></use>
                </svg>
                <div class="name">数据库</div>
                <div class="code-name">#icon-shujuku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wubiankuang"></use>
                </svg>
                <div class="name">无边框</div>
                <div class="code-name">#icon-wubiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingchuyangshi"></use>
                </svg>
                <div class="name">清除样式</div>
                <div class="code-name">#icon-qingchuyangshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenjuzhongduiqi"></use>
                </svg>
                <div class="name">文本居中对齐</div>
                <div class="code-name">#icon-wenbenjuzhongduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayin"></use>
                </svg>
                <div class="name">打印</div>
                <div class="code-name">#icon-dayin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenfenge"></use>
                </svg>
                <div class="name">文本分割</div>
                <div class="code-name">#icon-wenbenfenge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hanshu"></use>
                </svg>
                <div class="name">函数‘</div>
                <div class="code-name">#icon-hanshu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiangxu"></use>
                </svg>
                <div class="name">降序</div>
                <div class="code-name">#icon-jiangxu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingbuduiqi"></use>
                </svg>
                <div class="name">顶部对齐</div>
                <div class="code-name">#icon-dingbuduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#icon-tupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangxia90"></use>
                </svg>
                <div class="name">向下90</div>
                <div class="code-name">#icon-xiangxia90</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shupaiwenzi"></use>
                </svg>
                <div class="name">竖排文字</div>
                <div class="code-name">#icon-shupaiwenzi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanjiabiankuang"></use>
                </svg>
                <div class="name">全加边框</div>
                <div class="code-name">#icon-quanjiabiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shengxu"></use>
                </svg>
                <div class="name">升序</div>
                <div class="code-name">#icon-shengxu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caijian"></use>
                </svg>
                <div class="name">裁剪</div>
                <div class="code-name">#icon-caijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jine"></use>
                </svg>
                <div class="name">金额</div>
                <div class="code-name">#icon-jine</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caidan1"></use>
                </svg>
                <div class="name">菜单1</div>
                <div class="code-name">#icon-caidan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quxiaohebing"></use>
                </svg>
                <div class="name">取消合并</div>
                <div class="code-name">#icon-quxiaohebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenxiahuaxian"></use>
                </svg>
                <div class="name">文本下划线</div>
                <div class="code-name">#icon-wenbenxiahuaxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangbiankuang"></use>
                </svg>
                <div class="name">上边框</div>
                <div class="code-name">#icon-shangbiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingwei"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#icon-dingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sizhoujiabiankuang"></use>
                </svg>
                <div class="name">四周加边框</div>
                <div class="code-name">#icon-sizhoujiabiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cebianlanshouqi"></use>
                </svg>
                <div class="name">侧边栏收起</div>
                <div class="code-name">#icon-cebianlanshouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hebing"></use>
                </svg>
                <div class="name">合并</div>
                <div class="code-name">#icon-hebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangshangqingxie"></use>
                </svg>
                <div class="name">向上倾斜</div>
                <div class="code-name">#icon-xiangshangqingxie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuipingduiqi"></use>
                </svg>
                <div class="name">水平对齐</div>
                <div class="code-name">#icon-shuipingduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenshanchuxian"></use>
                </svg>
                <div class="name">文本删除线</div>
                <div class="code-name">#icon-wenbenshanchuxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenyouduiqi"></use>
                </svg>
                <div class="name">文本右对齐</div>
                <div class="code-name">#icon-wenbenyouduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qianjin"></use>
                </svg>
                <div class="name">前进</div>
                <div class="code-name">#icon-qianjin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tubiao"></use>
                </svg>
                <div class="name">图表</div>
                <div class="code-name">#icon-tubiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youbiankuang"></use>
                </svg>
                <div class="name">右边框</div>
                <div class="code-name">#icon-youbiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baifenhao"></use>
                </svg>
                <div class="name">百分号</div>
                <div class="code-name">#icon-baifenhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-geshishua"></use>
                </svg>
                <div class="name">格式刷</div>
                <div class="code-name">#icon-geshishua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baocun"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#icon-baocun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuyanzheng"></use>
                </svg>
                <div class="name">数据验证</div>
                <div class="code-name">#icon-shujuyanzheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jieduan"></use>
                </svg>
                <div class="name">截断</div>
                <div class="code-name">#icon-jieduan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-geshitiaojian"></use>
                </svg>
                <div class="name">格式条件</div>
                <div class="code-name">#icon-geshitiaojian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zidonghuanhang"></use>
                </svg>
                <div class="name">自动换行</div>
                <div class="code-name">#icon-zidonghuanhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cebianlanzhankai"></use>
                </svg>
                <div class="name">侧边栏展开</div>
                <div class="code-name">#icon-cebianlanzhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shaixuan2"></use>
                </svg>
                <div class="name">筛选2</div>
                <div class="code-name">#icon-shaixuan2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangxiaqingxie"></use>
                </svg>
                <div class="name">向下倾斜</div>
                <div class="code-name">#icon-xiangxiaqingxie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yichu"></use>
                </svg>
                <div class="name">溢出</div>
                <div class="code-name">#icon-yichu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuizhihebing"></use>
                </svg>
                <div class="name">垂直合并</div>
                <div class="code-name">#icon-chuizhihebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenfensanduiqi"></use>
                </svg>
                <div class="name">文本分散对齐</div>
                <div class="code-name">#icon-wenbenfensanduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuobiankuang"></use>
                </svg>
                <div class="name">左边框</div>
                <div class="code-name">#icon-zuobiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenyechakan"></use>
                </svg>
                <div class="name">分页查看</div>
                <div class="code-name">#icon-fenyechakan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yunhang"></use>
                </svg>
                <div class="name">运行</div>
                <div class="code-name">#icon-yunhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lie"></use>
                </svg>
                <div class="name">列</div>
                <div class="code-name">#icon-lie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-quanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shaixuan"></use>
                </svg>
                <div class="name">筛选</div>
                <div class="code-name">#icon-shaixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengxin"></use>
                </svg>
                <div class="name">更新</div>
                <div class="code-name">#icon-gengxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingchu"></use>
                </svg>
                <div class="name">清除</div>
                <div class="code-name">#icon-qingchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-hang"></use>
                </svg>
                <div class="name">行</div>
                <div class="code-name">#icon-hang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhushi"></use>
                </svg>
                <div class="name">注释</div>
                <div class="code-name">#icon-zhushi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jian"></use>
                </svg>
                <div class="name">剪</div>
                <div class="code-name">#icon-jian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jisuan"></use>
                </svg>
                <div class="name">计算</div>
                <div class="code-name">#icon-jisuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jia"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#icon-jia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dibuduiqi"></use>
                </svg>
                <div class="name">底部对齐</div>
                <div class="code-name">#icon-dibuduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangshang90"></use>
                </svg>
                <div class="name">向上90</div>
                <div class="code-name">#icon-xiangshang90</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wuxuanzhuang"></use>
                </svg>
                <div class="name">无选装</div>
                <div class="code-name">#icon-wuxuanzhuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xianshiyincangwangge"></use>
                </svg>
                <div class="name">显示隐藏网格</div>
                <div class="code-name">#icon-xianshiyincangwangge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dongjie"></use>
                </svg>
                <div class="name">冻结</div>
                <div class="code-name">#icon-dongjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenzuoduiqi"></use>
                </svg>
                <div class="name">文本左对齐</div>
                <div class="code-name">#icon-wenbenzuoduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-houtui"></use>
                </svg>
                <div class="name">后退</div>
                <div class="code-name">#icon-houtui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuipinghebing"></use>
                </svg>
                <div class="name">水平合并</div>
                <div class="code-name">#icon-shuipinghebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiabiankuang"></use>
                </svg>
                <div class="name">下边框</div>
                <div class="code-name">#icon-xiabiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shezhi"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#icon-shezhi</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
