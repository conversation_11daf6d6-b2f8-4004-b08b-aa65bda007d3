<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>智能工具使用说明</title>
	<link rel="stylesheet" href="../css/vant.css" />
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;background-color: #F8FAFB;overflow-y: auto; }
		.pageWarp{
			padding: 30px 10px;
			box-sizing: border-box;
			font-size: 16px;
		}
	</style>
</head>
<body>
	<div v-cloak id="app">
		<div class="pageWarp" v-html="content"></div>
	</div>
</body>
<script type="text/javascript" src="../script/vue.min.js"></script>
<script type="text/javascript" src="../script/vant.min.js"></script>
<script type="text/javascript" src="../script/axios.min.js"></script>
<script type="text/javascript" src="../script/url.js"></script>
<script type="text/javascript" src="../script/api.js"></script>
<script type="text/javascript">
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var vm,pageParam={};
	var vmData = {
		appUrl:appUrl,
		appTheme:datas.appTheme||"#3657C0",
		token:datas.token,
		areaId:datas.areaId,
		content:`<p style=\"text-align: center; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"center\"><span style=\"font-family: 方正小标宋_GBK; font-size: 22.0000pt;\"><span style=\"font-family: 方正小标宋_GBK;\">山东数字政协智能工具（试用版）使用说明</span></span></p>\n<p style=\"text-align: justify; text-indent: 2em; line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\">&nbsp;</p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">根据《</span><span style=\"font-family: 仿宋_GB2312;\">&ldquo;山东数字政协&rdquo;建设迭代升级方案》，将在数字政协建设中引入智能化应用工具，为政协委员及机关人员在日常工作履职中提供智能化辅助支撑。目前，基于通用智能模型搭建了智能工具测试版，</span><span style=\"font-family: 仿宋_GB2312;\">提供一键校正、资料推荐、关键词提取、文本比对、智能润色、智能扩写</span><span style=\"font-family: 仿宋_GB2312;\">6个模块</span><span style=\"font-family: 仿宋_GB2312;\">，同步支持电脑端和移动端。</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 黑体; font-size: 16.0000pt;\"><span style=\"font-family: 黑体;\">一、智能应用入口</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 楷体_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 楷体_GB2312;\">（一）电脑端</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">进入山东通工作台</span><span style=\"font-family: 仿宋_GB2312;\">--政协在线--智能工具，即可调用该工具。</span></span></p>\n<p style=\"line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\"><img src=\"https://productpc.cszysoft.com:8080/lzt/image/SHARE_MGw0NkxUTDdBMnM5bjJFa0FxYWQ1dmQyU1pEMy9VYW5TR2dBN2gzeW9VQVlrYU1hY3pUbm5QdCtLRHh6ME5OZw==\" width=\"100%\" /></p>\n<p style=\"line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\"><img src=\"https://productpc.cszysoft.com:8080/lzt/image/SHARE_NHZBRkZxWkVjcG9Qd2dwQjNCdjE5WStlNElNS1ViRVRqTXBnQnRiV01WWmNvcjQyOUVHYWpMcDQyVlBZVWtNWg==\" width=\"100%\" /></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 楷体_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 楷体_GB2312;\">（二）移动端</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">进入山东通工作台</span><span style=\"font-family: 仿宋_GB2312;\">--政协在线--智能工具，即可调用该工具（后续会采用悬浮图标形式展现）。</span></span></p>\n<p style=\"line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\"><img src=\"https://productpc.cszysoft.com:8080/lzt/image/SHARE_aXRLUUZJcjh6dTBBVTNWVVhNYUlDaDFrMkl4aGhSNExKamtmRjlDY2RGQnRneXI5WEg3bjBsVGNKNk1XSG1ONQ==\" width=\"100%\" /></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 黑体; font-size: 16.0000pt;\"><span style=\"font-family: 黑体;\">二、功能模块介绍</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 楷体_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 楷体_GB2312;\">（一）一键校正</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">点击进入一键校正功能</span><span style=\"font-family: 仿宋_GB2312;\">--粘贴文本--点击一键校正按钮即可实现文本校对功能，</span></span><span style=\"font-family: 仿宋_GB2312; color: #000000; font-size: 15.5000pt;\"><span style=\"font-family: 仿宋_GB2312;\">可用于提案撰写、公文起草等场景</span></span><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">。电脑端支持文本导入和复制两种方式，移动端仅支持文本复制。</span></span></p>\n<p style=\"text-align: justify; line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\"><img src=\"https://productpc.cszysoft.com:8080/lzt/image/SHARE_cGQ3ZTA5WEp4ZW9TS3luUlBrS3d5Z0d4MytYLzQrZEloeHlvWHdHSmtKSlByVTkwaTBQV1FLc1NSQ3gxYytnNA==\" width=\"100%\" /></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 楷体_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 楷体_GB2312;\">（二）资料推荐</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">点击进入资料推荐</span><span style=\"font-family: 仿宋_GB2312;\">--输入关注的关键词--点击搜索。系统</span></span><span style=\"font-family: 仿宋_GB2312; color: #000000; font-size: 15.5000pt;\"><span style=\"font-family: 仿宋_GB2312;\">已集成</span> <span style=\"font-family: 仿宋_GB2312;\">330 余万条数据，</span></span><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">可以通过资料推荐和对话生成式两种形式提供推荐结果。</span></span></p>\n<p style=\"text-align: justify; line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\"><img src=\"https://productpc.cszysoft.com:8080/lzt/image/SHARE_bUJKdUFoaFpXbDNzMEtON1k5aGlTMko5aTNiTHJTK3k0OFdVOGFERFE5SDlyTUlnZnhKVVFkdEt0WTRlUk1JaA==\" width=\"100%\" /></p>\n<p style=\"text-align: justify; text-indent: 2em; line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\">&nbsp;</p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 楷体_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 楷体_GB2312;\">（三）文本比对</span></span></p>\n<p style=\"text-indent: 31pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; color: #000000; font-size: 15.5000pt;\"><span style=\"font-family: 仿宋_GB2312;\">点击进入文本比对</span><span style=\"font-family: 仿宋_GB2312;\">--输入需要对比的前后两个版本的文本，点击一键比对，即可生成对照表。该模块主要应用于公文编撰环节，针对各版本差异性一键形成对照表，提高文本比对效率。</span></span></p>\n<p style=\"line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\"><img style=\"display: block; margin-left: auto; margin-right: auto;\" src=\"https://productpc.cszysoft.com:8080/lzt/image/SHARE_cUdkSGd2RTI4Y3gybnV4UkE0T2ZuZnVWVHo0MllsQWdUWjhDTjBVZWJzaklibzhuU2YyL2k4Y3NBWi9GQ2NoSQ==\" width=\"100%\" /></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 楷体_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 楷体_GB2312;\">（四）关键词提取</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">点击进入关键词提取</span><span style=\"font-family: 仿宋_GB2312;\">--输入文本--一键生成，即可生成关键词。</span></span><span style=\"font-family: 仿宋_GB2312; color: #000000; font-size: 15.5000pt;\"><span style=\"font-family: 仿宋_GB2312;\">该模块应用于数据标引语言的生成，便于形成数据标签。</span></span></p>\n<p style=\"text-align: justify; text-indent: 2em; line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\">&nbsp;</p>\n<p style=\"line-height: 1.5; font-size: 16pt; font-family: 仿宋_GB2312;\" align=\"justify\"><img style=\"display: block; margin-left: auto; margin-right: auto;\" src=\"https://productpc.cszysoft.com:8080/lzt/image/SHARE_cFBRbFVJLzlPb3h5QWp5Nk1XbVduc0d5MEtsNzBsSEM1aC9rc3pnSnhrcXRkeVRqaWVBZGFOVW8wMlVOdFBnQQ==\" width=\"100%\" /></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 楷体_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 楷体_GB2312;\">（五）智能润色</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">粘贴或者输入文本</span><span style=\"font-family: 仿宋_GB2312;\">--点击智能润色按钮即可对文章进行润色，左下角可设置字数限制。</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 楷体_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 楷体_GB2312;\">（六）智能扩写</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 30pt; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 仿宋_GB2312; font-size: 16.0000pt;\"><span style=\"font-family: 仿宋_GB2312;\">粘贴或者输入文本</span><span style=\"font-family: 仿宋_GB2312;\">--点击智能扩写按钮即可完成智能化扩写。目前扩写支持1500字以内，超过字数不再支持。</span></span></p>\n<p style=\"text-indent: 32pt; text-align: justify; line-height: 150%; margin: 0pt 0pt 0.0001pt; font-family: Calibri; font-size: 12pt;\" align=\"justify\"><span style=\"font-family: 宋体; line-height: 150%; font-size: 16.0000pt;\">&nbsp;</span></p>`,
	};
	var methods = {
		init:function(){
			var that = this;
		},
	};
	


	window.onload = function() {
		showVue();
	};
	
	function showVue(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
</script>
</html>