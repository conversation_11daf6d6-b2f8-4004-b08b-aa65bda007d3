<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>详情</title>
	<link rel="stylesheet" href="../css/<EMAIL>" />
	<link rel="stylesheet" href="./css/dsmarkdown.css" />
	<style type="text/css">
		html,page,:root,:host{ --van-primary-color:transparent; --van-primary-color-transparent:transparent;--van-width-page:600px; }
		[v-cloak] { display: none; }
		.click{ cursor: pointer; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;min-width: 360px; }
		.details_header{ padding: 16px; }
		.details_title{ font-size: 17px; color: #333; font-weight: 600; }
		.details_time{ margin-top: 7px; font-size: 14px; color: #666; }
		.van-tabs__nav{ justify-content: center; flex-flow: row; }
		.van-tab{ width: 100px; flex:none; font-size: 17px; }
		
		.buttons{display: flex;cursor: pointer;color: #909090;}
		.tabs_warp{ padding: 10px 16px; }
		.tabs_title_box{ display: flex;align-items: center; }
		.tabs_title{ font-weight: 600; font-size: 14px; color: #333333;flex:1; }
		.tabs_content{ background: rgba(143,158,178,0.08); border-radius: 6px 6px 6px 6px; padding: 15px; margin:10px 0; font-size: 16px; color: #333333; }
		.list_item{ margin-top: 15px; }
		.list_item_time{ font-weight: 400; font-size: 14px; color: #666666; margin-bottom: 6px; }
		.list_item_text{ font-weight: 400; font-size: 16px; color: #333333; }
		.todo_item_btn{ background: var(--van-primary-color); border-radius: 30px 30px 30px 30px; padding: 2px 10px; font-weight: 500; font-size: 14px; color: #FFFFFF; margin-top: 7px; width: 80px; text-align: center; }
		.original_item{ margin-top: 15px; }
		.details_bottom{
			z-index: 1; flex-direction: row; align-items: center; margin-top: auto; display: flex; position: -webkit-sticky; position: sticky; bottom: 0;background: #FFF;box-shadow: 0px -2px 4px 0px rgba(52,74,146,0.06);
			padding: 5px 6px;
		}
		.bottom_btns{ display: flex;align-items: center;justify-content: center; padding: 10px 10px; margin-right: 10px; }
		.bottom_time{ font-weight: 400; font-size: 12px; color: #666666; }
		.bottom_slider{ flex:1; margin: 0 10px; }
	</style>
</head>
<body>
	<div v-cloak id="app">
		<div v-if="title">
			<div class="details_header">
				<div class="details_title">{{title}}</div>
				<div class="details_time">{{time}}</div>
			</div>
			<van-tabs v-model:active="tabActive" scrollspy sticky color="#000" line-width="35px" line-height="2px">
				<van-tab title="智能总结">
					<div class="tabs_warp">
						<div class="tabs_title_box">
							<div class="tabs_title">总结内容</div>
							<!-- 复制 -->
							<div @click="copy(summarize)" title="复制" class="buttons">
								<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clipPath id="clip1248_20193"><rect width="17.052675" height="17.052441" transform="translate(1.000000 1.000000)" fill="white" fill-opacity="0"></rect></clipPath><clipPath id="clip1257_20794"><rect id="复制" width="20.000000" height="20.000000" fill="white" fill-opacity="0"></rect></clipPath></defs><g clip-path="url(#clip1257_20794)"><g clip-path="url(#clip1248_20193)"><path id="path" d="M5.03 14.64C4.77 14.64 4.5 14.62 4.24 14.56C3.98 14.51 3.73 14.43 3.49 14.33C3.24 14.23 3.01 14.1 2.79 13.96C2.57 13.81 2.37 13.64 2.18 13.45C1.99 13.26 1.82 13.05 1.68 12.83C1.53 12.61 1.4 12.37 1.3 12.13C1.2 11.88 1.13 11.63 1.07 11.36C1.02 11.1 1 10.84 1 10.57L1 5.07C1 4.8 1.02 4.54 1.07 4.27C1.13 4.01 1.2 3.76 1.3 3.51C1.4 3.26 1.53 3.03 1.68 2.81C1.82 2.58 1.99 2.38 2.18 2.19C2.37 2 2.57 1.83 2.79 1.68C3.01 1.53 3.24 1.41 3.49 1.31C3.73 1.2 3.98 1.13 4.24 1.07C4.5 1.02 4.77 1 5.03 1L10.49 1C10.75 1 11.01 1.02 11.27 1.07C11.53 1.13 11.78 1.2 12.03 1.31C12.27 1.41 12.51 1.53 12.73 1.68C12.95 1.83 13.15 2 13.34 2.19C13.53 2.38 13.69 2.58 13.84 2.81C13.99 3.03 14.11 3.26 14.21 3.51C14.31 3.76 14.39 4.01 14.44 4.27C14.5 4.54 14.52 4.8 14.52 5.07L12.94 5.07C12.94 4.91 12.92 4.75 12.89 4.58C12.86 4.43 12.81 4.27 12.75 4.12C12.69 3.97 12.61 3.83 12.52 3.69C12.43 3.56 12.33 3.43 12.22 3.32C12.1 3.2 11.98 3.1 11.85 3.01C11.71 2.92 11.57 2.84 11.42 2.78C11.27 2.72 11.12 2.67 10.96 2.64C10.81 2.61 10.65 2.59 10.49 2.59L5.03 2.59C4.87 2.59 4.71 2.61 4.55 2.64C4.4 2.67 4.24 2.72 4.09 2.78C3.95 2.84 3.8 2.92 3.67 3.01C3.54 3.1 3.41 3.2 3.3 3.32C3.18 3.43 3.08 3.56 2.99 3.69C2.9 3.83 2.83 3.97 2.77 4.12C2.71 4.27 2.66 4.43 2.63 4.58C2.6 4.75 2.58 4.91 2.58 5.07L2.58 10.57C2.58 10.73 2.6 10.89 2.63 11.05C2.66 11.21 2.71 11.37 2.77 11.52C2.83 11.67 2.9 11.81 2.99 11.94C3.08 12.08 3.18 12.2 3.3 12.32C3.41 12.43 3.54 12.54 3.67 12.63C3.8 12.72 3.95 12.79 4.09 12.86C4.24 12.92 4.4 12.96 4.55 13C4.71 13.03 4.87 13.04 5.03 13.04L5.03 14.64Z" fill="currentColor" fill-opacity="1.000000" fill-rule="evenodd"></path></g><path id="path" d="M14.75 18.91L9.3 18.91C9.03 18.91 8.77 18.88 8.51 18.83C8.25 18.78 8 18.7 7.75 18.6C7.51 18.49 7.27 18.37 7.05 18.22C6.83 18.07 6.63 17.9 6.44 17.71C6.25 17.52 6.09 17.32 5.94 17.1C5.79 16.87 5.67 16.64 5.57 16.39C5.47 16.14 5.39 15.89 5.34 15.63C5.28 15.37 5.26 15.1 5.26 14.83L5.26 9.33C5.26 9.06 5.28 8.8 5.34 8.54C5.39 8.28 5.47 8.02 5.57 7.77C5.67 7.53 5.79 7.29 5.94 7.07C6.09 6.85 6.25 6.64 6.44 6.45C6.63 6.26 6.83 6.09 7.05 5.95C7.27 5.8 7.51 5.67 7.75 5.57C8 5.47 8.25 5.39 8.51 5.34C8.77 5.29 9.03 5.26 9.3 5.26L14.75 5.26C15.01 5.26 15.28 5.29 15.54 5.34C15.8 5.39 16.05 5.47 16.29 5.57C16.54 5.67 16.77 5.8 16.99 5.95C17.21 6.09 17.41 6.26 17.6 6.45C17.79 6.64 17.96 6.85 18.1 7.07C18.25 7.29 18.37 7.53 18.48 7.77C18.58 8.02 18.65 8.28 18.71 8.54C18.76 8.8 18.78 9.06 18.78 9.33L18.78 14.83C18.78 15.1 18.76 15.37 18.71 15.63C18.65 15.89 18.58 16.14 18.48 16.39C18.37 16.64 18.25 16.87 18.1 17.1C17.96 17.32 17.79 17.52 17.6 17.71C17.41 17.9 17.21 18.07 16.99 18.22C16.77 18.37 16.54 18.49 16.29 18.6C16.05 18.7 15.8 18.78 15.54 18.83C15.28 18.88 15.01 18.91 14.75 18.91ZM9.3 6.86C9.13 6.86 8.97 6.87 8.82 6.91C8.66 6.94 8.51 6.98 8.36 7.05C8.21 7.11 8.07 7.18 7.93 7.28C7.8 7.37 7.68 7.47 7.56 7.58C7.45 7.7 7.35 7.82 7.26 7.96C7.17 8.09 7.09 8.24 7.03 8.38C6.97 8.54 6.92 8.69 6.89 8.85C6.86 9.01 6.84 9.17 6.84 9.33L6.84 14.83C6.84 15 6.86 15.16 6.89 15.32C6.92 15.48 6.97 15.63 7.03 15.78C7.09 15.93 7.17 16.07 7.26 16.21C7.35 16.34 7.45 16.47 7.56 16.58C7.68 16.7 7.8 16.8 7.93 16.89C8.07 16.98 8.21 17.06 8.36 17.12C8.51 17.18 8.66 17.23 8.82 17.26C8.97 17.29 9.13 17.31 9.3 17.31L14.75 17.31C14.91 17.31 15.07 17.29 15.23 17.26C15.38 17.23 15.54 17.18 15.69 17.12C15.83 17.06 15.98 16.98 16.11 16.89C16.24 16.8 16.37 16.7 16.48 16.58C16.59 16.47 16.7 16.34 16.79 16.21C16.87 16.07 16.95 15.93 17.01 15.78C17.07 15.63 17.12 15.48 17.15 15.32C17.18 15.16 17.2 15 17.2 14.83L17.2 9.33C17.2 9.17 17.18 9.01 17.15 8.85C17.12 8.69 17.07 8.54 17.01 8.38C16.95 8.24 16.87 8.09 16.79 7.96C16.7 7.82 16.59 7.7 16.48 7.58C16.37 7.47 16.24 7.37 16.11 7.28C15.98 7.19 15.83 7.11 15.69 7.05C15.54 6.98 15.38 6.94 15.23 6.91C15.07 6.87 14.91 6.86 14.75 6.86L9.3 6.86Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path></g></svg>
							</div>
						</div>
						<div class="tabs_content" v-html="summarize"></div>

						<template v-if="todo.length">
							<div class="tabs_title">待办</div>
							<div v-for="(item,index) in todo" class="list_item">
								<div class="list_item_text">{{item.description}}</div>
								<div @click="openTask(item)" class="todo_item_btn" >生成任务 <van-icon name="arrow" size="12"/> </div>
							</div>
						</template>
					</div>
				</van-tab>
				<van-tab title="原文">
					<div class="tabs_warp">
						<div class="tabs_title">原文记录</div>
						<div v-for="(item,index) in listData" class="list_item">
							<div class="list_item_time">{{formatTime(Math.round(Number(item.startTime)/1000))}}</div>
							<div class="list_item_text">{{item.text}}</div>
						</div>
					</div>
				</van-tab>
			</van-tabs>
			<div v-if="playPath" class="details_bottom" :style="`padding-bottom:${safeBottom+5}px;`">
				<div @click="playRecord()" class="bottom_btns">
					<van-icon :name="isPlay?'pause':'play'" size="24px"/>
				</div>
				<div class="bottom_time">{{formatTime(nowTime)}}</div>
				<div class="bottom_slider">
					<van-slider v-if="allTime" v-model="nowTime" :min="0" :max="allTime" bar-height="4px" button-size="12px" active-color="#666666" inactive-color="#EEEEEE" @drag-start="dragState(true)" @drag-end="dragState(false)" @change="audioChange" />
				</div>
				<div class="bottom_time">{{formatTime(allTime)}}</div>
			</div>
		</div>


		<van-popup style="width: 100%;height: 100%;background-color: rgba(255,255,255,0.6);" v-model:show="loading" :overlay="false" :close-on-click-overlay="false">
			<div style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;">
				<van-loading size="34px"/>
			</div>
		</van-popup>

	</div>
</body>
<script type="text/javascript" src="../script/<EMAIL>"></script>
<script type="text/javascript" src="../script/<EMAIL>"></script>
<script type="text/javascript" src="../script/axios.min.js"></script>
<script type="text/javascript" src="../script/url.js"></script>
<script type="text/javascript" src="../script/jquery-3.6.4.min.js"></script>
<script type="text/javascript" src="../script/markdown-it.min.js"></script>
<script type="text/javascript" src="../script/dayjs.js"></script>
<script type="text/javascript" src="./js/aiGeneral.js"></script>
<script type="text/javascript">
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var audio;
	const md = markdownit({
		html: true, // 允许 HTML 标签
		linkify: true // 自动转换 URL 为链接
	});
	
	var vmData = {
		safeBottom:0,
		appUrl:appUrl,
		appTheme:datas.appTheme||"#3657C0",
		token:datas.token,
		areaId:datas.areaId,
		title:"",
		time:"",
		tabActive:0,

		loading:false,

		summarize:"",
		todo:[
			// {text:"1.本周内用人工智能生成前端代码和页面效果"},
			// {text:"2.拉吴勇一起做出福昕扇板demo效果"},
		],
		listData:[
			// {startTime:"320",endTime:"3000",text:"现在我们来开产品的一个讨论会，这次讨论会主要就是沟通一下产品几条产品线，我们接下来要做哪些智能化的应用?比如说人大产品线有哪些智能化的应用?政协产品线有哪些智能化的应用?数字会务产品线又有哪些智能化的应用?我们把这些应用就是整理成一个清单然后再去，嗯，进行一个梳理和确认。"},
			// {startTime:"4000",endTime:"8000",text:"现在我们来开产品的一个讨论会，这次讨论会主要就是沟通一下产品几条产品线，我们接下来要做哪些智能化的应用?比如说人大产品线有哪些智能化的应用?政协产品线有哪些智能化的应用?数字会务产品线又有哪些智能化的应用?我们把这些应用就是整理成一个清单然后再去，嗯，进行一个梳理和确认。"},
		],

		playPath:"",//播放地址
		isPlay:false,//是否播放
		nowTime:0,
		allTime:0,
		
	};
	var vmWatch = {
		playPath(_value){
			if(_value){
				this.createAudio();
			}
		}
	};
	var methods = {
		init(){
			var that = this;
			that.loading = true;
			that.ajax(`${that.appUrl}aigptChatLogs/info`,
			{
				detailId:datas.id,
			},(ret,err)=>{
				that.loading = false;
				if(!ret || ret.code != 200){
					toast((ret ? ret.message || ret.msg || ret.body || ret.data : err.msg || err.body) || "打开失败");
					return;
				}
				var data = ret.data || {};
				var voices = (data.voiceText||"").split('|zy|');
				var voiceIds = data.voiceIds || "";
				that.title = voices[0];
				that.time = dayjs(data.createDate).format('YYYY-MM-DD HH:mm');

				
				try{
					that.summarize = dealMark(data.answer || "");
				}catch(e){
					that.summarize = (data.answer || "");
				}
				if(voiceIds){
					that.playPath = `${that.appUrl}file/preview/${voiceIds}?authorization=${that.token}`;
				}
				that.listData = JSON.parse(voices[2]);

				that.ajax(`${that.appUrl}aigpt/chat`,
				{
					chatId:generateSessionID(),
					chatBusinessScene:"meet_task_chat",
					question:data.answer,
				},(ret,err)=>{
					if(ret && ret.code == 200){
						var choice = ret.data.choices || [{}];
						var details = choice[0].message || {};
						if (details.hasOwnProperty('content')){
							try{
								that.todo = JSON.parse(details.content);
							}catch(e){
								that.todo = [];
							}
						}
					}
				});
			});
		},
		
		//播放音频
		playRecord(){
			var that = this;
			if(!that.allTime){
				toast(window.audioErr || "准备中");
				return;
			}
			that.isPlay = !that.isPlay;
			if(that.isPlay){
				audio.play();
			}else{
				audio.pause();
			}
		},
		//创建音频播放器
		createAudio(){
			var that = this;
			fetch(that.playPath).then(response => {
				return response.blob();
			}).then(blob => {
				// audio = new Audio(that.playPath);
				const correctedBlob = new Blob([blob], {
					type: 'audio/wav' // 修改为你需要的类型
				});
				audio = new Audio(URL.createObjectURL(correctedBlob));
				// 初始化进度条
				audio.addEventListener('loadedmetadata', () => {
					that.allTime = Math.floor(audio.duration);
				});
				// 更新进度条和时间显示
				audio.addEventListener('timeupdate', () => {
					if(!window.dragState){
						that.nowTime = Math.floor(audio.currentTime);
					}
				});
				// 监听播放完成事件
				audio.addEventListener('ended', () => {
					that.nowTime = 0;
					that.isPlay = false;
				});
				// 通用错误监听
				audio.addEventListener('error', () => {
					console.log(audio.error);
					window.audioErr = audio.error.code + ":" + audio.error.message;
				});
			});
		},
		//手动拖动
		audioChange(){
			var that = this;
			audio.currentTime = that.nowTime;
		},
		dragState(_type){
			window.dragState = _type;
		},
		//打开任务按钮
		openTask(_item){
			if(isApp){
				api.openWin({
					name: 'mo_add_n',
					url:'widget://pages/mo_add_n/mo_add_n.stml',
					pageParam:{
						...{paramType:"MeetingBacklog"},
						..._item
					}
				})
			}else if(window.basePage){
				window.basePage.api.openWin({
					name: 'mo_add_n',
					url:'mo_add_n.stml',
					pageParam:{
						...{paramType:"MeetingBacklog"},
						..._item
					}
				})
			}else{
				toast("该功能本端暂不支持，请使用APP");
			}
		},
	};

	
</script>
</html>