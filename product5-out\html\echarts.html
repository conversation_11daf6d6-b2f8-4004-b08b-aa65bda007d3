<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>demo</title>
	<style type="text/css">
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0; }
		[v-cloak] { display: none; }
	</style>
</head>
<body>
	<div id="app">
		<v-chart ref="echart" style="width: 100%;height: 100%;" :options="chart"></v-chart>
	</div>
</body>
<script type="text/javascript" src="./script/vue.min.js"></script>
<script type="text/javascript" src="./script/echarts.min.js"></script>
<script type="text/javascript" src="./script/echarts-wordcloud.min.js"></script>
<script type="text/javascript" src="./script/vue-echarts.min.js"></script>
<script type="text/javascript">
	Vue.component('v-chart', VueECharts);
	var vm,pageParam={};
	var vmData = {
		chart: {},
	};
	var methods = {
		init:function(){
			var echart = this.$refs["echart"].chart;
			//console.log(echart);
			var closeTask = null;
			function disTask(params){
				//console.log(params.type);
				clearTimeout(closeTask);
				closeTask = setTimeout(function(){
					//echart._dom.childNodes[1].style.display = 'none'
					echart.dispatchAction({
						type: 'hideTip'
					});
				},2000);
			}
			echart.getZr().on('click', disTask);
			echart.getZr().on('mousedown', disTask);
			echart.getZr().on('mousemove', disTask);
			echart.getZr().on('mouseup', disTask);
			echart.getZr().on('mouseout', disTask);
		},
	};

	window.onload = function() {
		setTimeout(() => {
			var pageParam = {};
			try{
				pageParam = api.pageParam;
				vmData.chart = pageParam.chart || {};
				api.addEventListener({
					name:pageParam.name+'_changeEcharts'
				}, function(ret){
					vmData.chart = ret.value.chart;
				})
			}catch(e){
				
			}
			showChart();
		}, 300);
	};
	
	function showChart(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
	window.addEventListener('message', function(event) {
		vmData.chart = JSON.parse(event.data).chart;
	});
</script>
</html>