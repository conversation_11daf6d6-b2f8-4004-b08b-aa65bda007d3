var vm,pageParam={},isApp,isWeb;
var zyTencentAsrRealtime,voiceRecognizer;

var GvmData = {

};

var GvmWatch = {

};

var Gmethods = {
	//复制
	async copy(_text){
		var that = this;
		var text = that.convertTextarea(_text);
		copyText(_text,text);
	},
	//颜色转成rgba
	colorRgba(color,alpha){
		return colorRgba(color,alpha);
	},
	//转换html为
	convertTextarea(value){
		var result = value.replace(/<br\s*\/?>/gi, '\n');
		result = result.replace(/<\/?p>/gi, '\n');
		result = result.replace(/<\/?div>/gi, '\n');
		result = result.replace(/<[^>]*>/g, '');
		result = result.replace(/(\n\s*)+/g, '\n\n');
		result = result.trim();
		return result;
	},
	//转换录音记要显示时间
	formatTime(totalSeconds,_showHours) {
		const hours = Math.floor(totalSeconds / 3600);
		const remainingSeconds = totalSeconds % 3600;
		const minutes = Math.floor(remainingSeconds / 60);
		const seconds = remainingSeconds % 60;
		const parts = (_showHours || hours > 0) ? [hours, minutes, seconds] : [minutes, seconds];
		return parts.map(part => part.toString().padStart(2, '0')).join(':');
	},
	//请求封装
	ajax(_url,_param,_callback,_header){
		var that = this;
		console.log(_url);
		console.log(JSON.stringify(_param));
		var callRet = (ret)=>{
			if(ret && ret.code == 200){
				var data = ret.data || "";
				var isEncrypt = ret.isEncrypt;
				if(isEncrypt && ret.data){
					try{
						ret.data = new SM4Util().decryptData_CBC(ret.data);
						ret.data = JSON.parse(ret.data);
					}catch(e){
						ret.data = ret.data;
					}
				}
			}
			console.log(isApp?JSON.stringify(ret):ret);
			_callback(ret,null);
		}
		if(isApp){
			var ajaxParam = {
				url:_url,
				method:"post",
				headers:{
					...{
						"u-login-areaId": that.areaId,
						"Authorization": that.token,
						"u-terminal":"APP",
					},
					...(_header||{})
				},
				data:_param
			};
			if(!_param.hasOwnProperty('files')){
				ajaxParam.headers["content-type"] = "application/json";
				ajaxParam.data = {body:JSON.stringify(_param)}
			}
			api.ajax(ajaxParam, function(ret, err){
				if(err){
					_callback(null,err||{});
					return;
				}
				callRet(ret);
			})
		}else{
			axios.post( _url, _param,
				{
					headers: {
						...{
							"content-type":"application/json",
							"u-login-areaId": that.areaId,
							"Authorization": that.token,
							"u-terminal":"APP",
						},
						...(_header||{})
					},
				}
			).catch(function (err) {
				_callback(null,err||{});
			}).then(function (ret) {
				try{
					ret = ret.data || {};
				}catch(e){
					console.log(ret);
					_callback(null,ret||{});
					return;
				}
				callRet(ret);
			});
		}
	},
};

window.onload = function() {
	showVue();
	try{
		api = window.parent.api;
		if(api){
			initApp();
		}
	}catch(e){
		console.log(e);
	}
};

apiready = function(){
	initApp();
}

function initApp(){
	vm.safeBottom = api.safeArea.bottom;
	if(api.platform == 'app'){
		zyTencentAsrRealtime = api.require('zyTencentAsrRealtime');
		voiceRecognizer = api.require('voiceRecognizer');
		isApp = true;
	}else{
		isWeb = true;
		getBasePage(window.parent);
	}
}

//app的web获取最上级页面
function getBasePage(_page){
	if(_page == _page.parent)
		return;
	if(!_page.apiweb){
		getBasePage(_page.parent);
	}else{
		window.basePage = _page;
	}
}

function showVue(){
	// 创建Vue应用
	const { createApp } = Vue;
	const app = createApp({
		data: ()=>{return {...vmData,...GvmData}},
		watch:{...vmWatch,...GvmWatch},
		mounted: function () {
			document.documentElement.style.setProperty('--van-primary-color', this.appTheme);
			document.documentElement.style.setProperty('--van-primary-color-transparent', colorRgba(this.appTheme,0.1));
			//获取asr配置
			this.ajax(`${this.appUrl}config/read`,{ codes:["tencent_asr"], },(ret,err)=>{
				if(ret && ret.code == "200"){
					window.asrKeys = ret.data.tencent_asr || "";
				}
			});
			this.init();
		},
		methods: {...methods,...Gmethods}
	});
	// 使用Vant组件
	app.use(vant);
	try{
		app.component('v-chart', VueECharts);
	}catch(e){

	}
	// 挂载Vue应用
	vm = app.mount('#app');
}

//获取当前文件夹路径页面
function getBaseUrl(){
	return location.href.substring(0,location.href.indexOf('smart_tools')) + "smart_tools";
}

//适配app打开新页面
function openWin(_url,_param){
	var name = "mo_details_url"+Math.floor(Math.random() * 100000000);
	var param = {
		dotMore:"true",
		url:_url
	};
	if(isApp){
		api.openWin({
			name: name,
			url:'widget://pages/mo_details_url/mo_details_url.stml',
			pageParam:{...param,...(_param||{})},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false,//ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},
			reload: true,	// 去除设置
			allowEdit: true,	//去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		})
	}else if(window.basePage){
		window.basePage.api.openWin({
			name: name,
			url:'mo_details_url.stml',
			pageParam:param
		})
	}else{
		window.location = _url;
	}
}


function toast(_text,_type,_position){
	vant.showToast({
		type:_type || "text",
		message: _text || '',
		position: _position || 'middle',
	});
}

function alert(_option,_callback){
	vant.showConfirmDialog({
		title: _option.title || '提示',
		message: _option.msg || _option.message || '',
	}).then(() => {
		_callback({buttonIndex:1});
	}).catch(() => {
		// on cancel
	});
}

// 生成一次性随机标识符
function generateSessionID() {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
		var r = Math.random() * 16 | 0,
			v = c === 'x' ? r : (r & 0x3 | 0x8);
		return v.toString(16);
	});
}

//颜色转成rgba
function colorRgba(_color, _alpha) {
	if (!_color) return;
	var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
	var color = _color.toLowerCase();
	if (reg.test(color)) {
		if (color.length === 4) {
			var colorNew = "#";
			for (var i = 1; i < 4; i += 1) {
				colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
			}
			color = colorNew;
		}
		var colorChange = [];
		for (var i = 1; i < 7; i += 2) {
			colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
		}
		return "rgba(" + colorChange.join(",") + "," + (_alpha || "1") + ")";
	} else {
		return color;
	}
}

const copyText = (_html,_text) => {
	if (navigator.clipboard && navigator.permissions) {
		// console.log(_html);
		// console.log(_text);
		navigator.clipboard.write([
			new ClipboardItem({
				'text/html': new Blob([_html], { type: 'text/html' }),
				'text/plain': new Blob([_text], { type: 'text/plain' })
			})
		]).then(() => toast("复制成功"))
		// navigator.clipboard.writeText(val).then(() => toast("复制成功"))
	} else {
		const textArea = document.createElement('textArea');
		textArea.value = _text;
		textArea.style.width = 0;
		textArea.style.position = 'fixed';
		textArea.style.left = '-999px';
		textArea.style.top = '10px';
		textArea.setAttribute('readonly', 'readonly');
		document.body.appendChild(textArea);
		textArea.select();
		try {
			var successful = document.execCommand('copy');
			toast(successful?"复制成功":"复制失败");
		} catch (err) {
			console.error("执行复制时发生错误:", err);
		}
		document.body.removeChild(textArea);
	}
}

//md转html
function dealMark(_text){
	var result = md.render(_text)
	result = result.replace(/<p\b[^>]*>([\s\S]*?)<\/p>/gi, (match, p1) => {
		return match.replace(/\n/g, '<br>');
	});
	return result;
}

const APPID = "5b581b4e";
const API_SECRET = "eee998bdcdd78c375f7488e5a4eb294e";
const API_KEY = "5edcf0f7b2364482a5b35528ae6a8b67";

function getWebSocketUrl(_type) {
	// 请求地址根据语种不同变化
	var url = `wss://${_type}-api.xfyun.cn/v2/${_type}`;
	var host = location.host;
	var date = new Date().toGMTString();
	var algorithm = "hmac-sha256";
	var headers = "host date request-line";
	var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/${_type} HTTP/1.1`;
	var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, API_SECRET);
	var signature = CryptoJS.enc.Base64.stringify(signatureSha);
	var authorizationOrigin = `api_key="${API_KEY}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
	var authorization = btoa(authorizationOrigin);
	url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
	return url;
}

//start 语音听写>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
var iatWS,recorder,resultText,resultTextTemp;
function startRecognize(_callback){
	resultText = "";
    resultTextTemp = "";
	//处理为app的情况 
	if((zyTencentAsrRealtime && window.asrKeys) || voiceRecognizer){//voiceRecognizer zyTencentAsrRealtime
		var granted = api.hasPermission({ list:['microphone'] })[0].granted;
		if(!granted){//判断权限
			api.requestPermission({ list:['microphone'] }, function(ret, err){
				if (ret.list[0].granted) {
					startRecognize(_callback);
				}else{
					_callback("",true);
				}
			});
			return;
		}
		if((zyTencentAsrRealtime && window.asrKeys)){
			var keys = window.asrKeys.split(',');
			zyTencentAsrRealtime.init({
				appId:keys[0],
				secretId:keys[1],
				secretKey:keys[2],
				isSaveAudioRecordFiles:false,//不保存音频文件 
			});
			zyTencentAsrRealtime.addEventListener(function(ret,err){
				if(ret.status == "recognizeResult"){//实时识别回调
					if(ret.eventType == "sliceSuccess"){//返回分片的识别结果，此为中间态结果，会被持续修正
						// console.log(JSON.stringify(ret));
						resultTextTemp = ret.text;
						_callback(resultText + resultTextTemp);
					}else if(ret.eventType == "segmentSuccess"){//返回语音流的识别结果，此为稳定态结果，可做为识别结果用与业务
						// console.log(JSON.stringify(ret));
						resultTextTemp = "";
						resultText += ret.text;
						_callback(resultText);
					}else if(ret.eventType == "success"){//识别结束回调，返回所有的识别结果
						// console.log(JSON.stringify(ret));
					}else if(ret.eventType == "failure"){//识别失败
						// console.log(JSON.stringify(ret));
						_callback("",true);
					}
				}
			});
			zyTencentAsrRealtime.start();
		}else if(voiceRecognizer){
			voiceRecognizer.createUtility({ ios_appid: '5b581b4e', android_appid: '5b581b4e' }, (ret, err)=> {
				voiceRecognizer.recognizeConfig({
					config: {
						vadbos: '10000',
						vadeos: '10000',
						timeout: '30000',
						netTimeout: '20000',
						rate: '16000',
						dot: true
					}
				},  (ret)=> {
					voiceRecognizer.recognizeStart();
				});
			});
			voiceRecognizer.addEventListener({ name: 'recognizeResult', realTime: true },  (ret)=> {
				ret = ret.recognizeResult;
				// console.log("recognizeResult："+JSON.stringify(ret));
				resultText += ret.result;
				_callback(resultText);
				if(ret.isLast){//最后一次
					_callback("",true);
				}
			});
			voiceRecognizer.addEventListener({ name: 'onError' },  (ret)=> {
				console.log("onError：" + JSON.stringify(ret));
				_callback("",true);
			});
			voiceRecognizer.addEventListener({ name: 'onEndOfSpeech' },  (ret)=> {
				console.log("onEndOfSpeech：" + JSON.stringify(ret));
			});
		}
		return;
	}

	recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
		if (iatWS.readyState === iatWS.OPEN) {
			iatWS.send(
				JSON.stringify({
					data: {
					status: isLastFrame ? 2 : 1,
					format: "audio/L16;rate=16000",
					encoding: "raw",
					audio: toBase64(frameBuffer),
					},
				})
			);
		}
	};
	recorder.onStop = () => {
		//_callback("",true);
	};
	const url = getWebSocketUrl("iat");
	if ("WebSocket" in window) {
		iatWS = new WebSocket(url);
	} else if ("MozWebSocket" in window) {
		iatWS = new MozWebSocket(url);
	} else {
		alert("浏览器不支持WebSocket");
		return;
	}
	iatWS.onopen = (e) => {
		// 开始录音
		recorder.start({
			sampleRate: 16000,
			frameSize: 1280,
		});
		var params = {
			common: {
				app_id: APPID,
			},
			business: {
				language: "zh_cn",
				domain: "iat",
				accent: "mandarin",
				vad_eos: 10000,
				dwa: "wpgs",
			},
			data: {
				status: 0,
				format: "audio/L16;rate=16000",
				encoding: "raw",
			},
		};
		iatWS.send(JSON.stringify(params));
	};
	iatWS.onmessage = (e) => {
		// 识别结束
		let jsonData = JSON.parse(e.data);
		if (jsonData.data && jsonData.data.result) {
			let data = jsonData.data.result;
			let str = "";
			let ws = data.ws;
			for (let i = 0; i < ws.length; i++) {
				str = str + ws[i].cw[0].w;
			}
			// 开启wpgs会有此字段(前提：在控制台开通动态修正功能)
			// 取值为 "apd"时表示该片结果是追加到前面的最终结果；取值为"rpl" 时表示替换前面的部分结果，替换范围为rg字段
			if (data.pgs) {
				if (data.pgs === "apd") {
					// 将resultTextTemp同步给resultText
					resultText = resultTextTemp;
				}
				// 将结果存储在resultTextTemp中
				resultTextTemp = resultText + str;
			} else {
				resultText = resultText + str;
			}
			_callback(resultTextTemp || resultText || "");
		}
	};
	iatWS.onerror = (e) => {
		console.error(e);
		recorder.stop();
		_callback("",true);
	};
	iatWS.onclose = (e) => {
		console.log(e);
		recorder.stop();
		if(e.code == 1000){//静默被停止   1005关闭socket被停止
			_callback("",true);
		}
	};
}
function stopRecognize(){
	if((zyTencentAsrRealtime && window.asrKeys)){
		zyTencentAsrRealtime.stop();
	}else if(voiceRecognizer){
		voiceRecognizer.recognizeStop();
	}else{
		iatWS && iatWS.close();
	}
}
//end >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

function toBase64(buffer) {
	var binary = "";
	var bytes = new Uint8Array(buffer);
	var len = bytes.byteLength;
	for (var i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return window.btoa(binary);
}

const srcPath = "./js";
function loadScripts(scripts, callback) {
	var loadedCount = 0;
	scripts.forEach(function(src) {
		var script = document.createElement('script');
		script.src = srcPath+"/"+src;
		script.async = false; // 确保按顺序加载，如果需要按顺序执行
		// 监听 script 的 onload 事件，脚本加载完成时触发
		script.onload = function() {
			loadedCount++;
			if (loadedCount === scripts.length) {
				callback(); // 当所有脚本加载完成后，执行回调
			}
		};
		// 监听加载错误事件
		script.onerror = function() {
			console.error('Error loading script: ' + src);
		};
		document.head.appendChild(script);
	});
}

loadScripts([
	'index.umd.iat.js'
],function(){
	recorder = new RecorderManager(srcPath);
});
