<!DOCTYPE html>
<html>
<script>
//字体适配 ===========================================================
(function (doc, win) {var docEl = doc.documentElement,resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',recalc = function () {var clientWidth = docEl.clientWidth;if(clientWidth > 480){clientWidth = 480;}if (!clientWidth) return;docEl.style.fontSize = 100 * (clientWidth / 360) + 'px';};if (!doc.addEventListener) return;win.addEventListener(resizeEvt, recalc, false);doc.addEventListener('DOMContentLoaded', recalc, false);})(document, window);

//动态加载文件
var dynamicLoading = {css : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var link = document.createElement('link');link.href = path;link.rel = 'stylesheet';link.type = 'text/css';head.appendChild(link);},js : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var script = document.createElement('script');script.src = path;script.type = 'text/javascript';script.async = false;head.appendChild(script);}}

//接收页面参数
var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

//外部文件 动态加载		css	js
dynamicLoading.css('../css/general.css?app_version='+app_version+'');
dynamicLoading.css('https://cdn.staticfile.org/vant/2.12.5/index.min.css?app_version='+app_version+'');

dynamicLoading.js('https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vue/2.6.14/vue.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vant/2.12.5/vant.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/dayjs/1.11.0/dayjs.min.js?app_version='+app_version+'');
dynamicLoading.js('../script/api.js?app_version='+app_version+'');	
dynamicLoading.js('../script/myjs.js?app_version='+app_version+'');	
dynamicLoading.js('../script/t.js?app_version='+app_version+'');	
dynamicLoading.js('../script/encryption.js?app_version='+app_version+'');	
dynamicLoading.js('../script/general.js?app_version='+app_version+'');
//在apicloud中打开	就再加载一次配置
apiready = function(){
	window.appLoading = true;
	initApp();
}
function initApp(){
	if(window.appLoading && window.webLoad){
		setTimeout(function(){
			try{vm.initApp();}catch(e){console.error(e.message)}
		},0);
	}
}
</script>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title></title>
	<style>
		.none{display:none;}
		.list_ok_box{ padding: 0.1rem 0; }
		.list_ok_warp{ padding: 0 0.15rem 0.1rem; }
		.list_ok_item{ background: #FFFFFF; box-shadow: 0px 2px 10px 1px rgba(24,64,118,0.08); border-radius: 0.04rem; padding:0.1rem; }
	</style>
</head>
<body>
	<div id="app" class="none">
		<div v-if="listData.length" class="list_ok_box">
			<div v-for="(item,index) in listData" @click="annexOpen({name:item.oldName,url:item.id})" class="list_ok_warp">
				<div class="list_ok_item">
					<div :style="loadConfiguration()+'color:#333;font-weight: 600;'">{{item.oldName}}</div>
					<div class="flex_box flex_align_center" style="margin-top:0.1rem;">
						<div :style="loadConfiguration(-2)+'color:#666;'"></div>
						<div class="flex_placeholder"></div>
						<div :style="loadConfiguration(-2)+'color:#666;'">年度：{{pageParam.year}}</div>
					</div>
				</div>
			</div>
		</div>
		<!--加载中提示 首次为骨架屏-->
		<div v-if="showSkeleton" class="notText">
			<van-skeleton v-for="(item,index) in 3" title :row="3"></van-skeleton>
		</div>
		<template v-else-if="!listData.length">
			<van-empty :style="loadConfiguration(-2)" :image="pageNot.url || pageNot.data[pageNot.type].url">
				<template #description>
					<div class="van-empty__description_text" :style="loadConfiguration(-1)" v-html="pageNot.text || pageNot.data[pageNot.type].text"></div>
					<div class="van-empty__description_summary" :style="loadConfiguration(-3)" v-html="pageNot.summary || pageNot.data[pageNot.type].summary"></div>
				</template>
				<div v-if="pageNot.hasBtn" :style="loadConfiguration(-1)">
					<van-button v-if="(pageNot.type==2||pageNot.type==3)&&pageType=='page'" @click.stop="T.closeWin()" round type="info" size="large" :color="appTheme">{{'返回'}}</van-button>
					<van-button v-else-if="pageNot.type==1||pageNot.type==4" @click.stop="getData();" round type="info" size="large" :color="appTheme">{{'刷新'}}</van-button>
				</div>
			</van-empty>
		</template>
		<div v-else class="notText" :style="loadConfiguration(-4)" v-html="pageNot.text" @click="loadMore()"></div>
	</div>
</body>
<script type="text/javascript">
	var vmData = {
		listData:[]
	};
	var vmWatch={
	};
	var vmMethods = {
		init:function(){
			var that = this;
			//刷新返回当前对象
			document.title = "审议报告";
			that.getData();
		},
		getData:function(_type){
			var that = this;
			if(!_type){
				that.pageNo = 1;
			}
			var url = zyUrl.getAppUrl() + "superviseAuditFile/list?";
			var postParam = {
				"pageNo":that.pageNo,
				"pageSize": !_type ? that.refreshPageSize > that.pageSize ? that.refreshPageSize : that.pageSize : that.pageSize,
				"keyword":that.seachText,
				"belongYear":that.pageParam.year,
				// "fileType":"1"
			};
			T.ajax({ u: url }, 'superviseAuditFile/list', function (ret, err) {
				T.refreshHeaderLoadDone();
				T.hideProgress();
				that.showSkeleton = false;
				var code = ret ? ret.errcode : "";
				var data = ret?ret.data||[]:[];
				var dataLength = data ? data.length : 0;
				that.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1;//类型 列表中只有有网和无网的情况
				that.pageNot.text = ret && code != 200 ? ret.errmsg || ret.data : "";//只有接口报的异常才改文字
				if (!_type) {//有时候 会出现加载2次网络的情况(缓存1次 网络1次) 导致页数不正确 这里再重置为1
					that.pageNo = 1;
					that.listData = [];
					if(!window.noScroll){
						window.noScroll = true;
						scrollToView();
					}
				}
				if (T.isArray(data) && data.length != 0) {
					data.forEach(function (_eItem) {//item index 原数组对象
						(_eItem.attachmentInfos||[]).forEach(function (_aItem) {
							that.listData.push(_aItem);
						});
					});
					that.pageNo++;
					that.pageNot.text = that.listData.length == 0 ? "" : data.length >= postParam.pageSize ? T.LOAD_MORE : T.LOAD_ALL;//当前返回的数量 等于 请求的数量 说明可能还有 少于说明没有了
					that.cleanIosDelay();
					that.refreshPageSize = Math.ceil(that.listData.length / that.pageSize) * that.pageSize, that.pageNo = Math.ceil(that.listData.length / that.pageSize) + 1;
				} else if (_type == 1) {//加载更多的时候 底部显示文字
					that.pageNot.text = ret ? code == 200 ? T.LOAD_ALL : ret.errmsg : T.NET_ERR;
				}
			}, '列表', "get", postParam);
		},
		//点击加载更多 或 上拉
		loadMore: function () {
			var that = this;
			if ((that.pageNot.text == T.LOAD_MORE || that.pageNot.text == T.NET_ERR) && that.pageNo != 1) {
				that.pageNot.text = T.LOAD_ING;
				that.getData(that.listData.length != 0 ? 1 : 0);//列表没数据时 算下拉 有数据上拉
			}
		},
	};
</script>
</html>