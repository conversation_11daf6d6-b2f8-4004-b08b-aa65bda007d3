<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>一键校正</title>
	<link rel="stylesheet" href="../css/vant.css" />
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;background-color: #F8FAFB;overflow-y: auto; }
		.pageWarp{ display: flex; flex-direction: column; width: 100%; height: 100%; }
		.van-field__body,.van-cell{height: 100%;}
		textarea {font-size: 14px;line-height: 24px;height:100%;color: #333;}
		.textareaDiv {font-size: 14px;height:100%;overflow-y: auto;color: #333;}
		p{ line-height: 24px;margin-block-start: 0; margin-block-end: 0; }
		.button_box{ display: flex; align-items: center;justify-content: center; padding: 16px; background-color: #FFF; }
		.van-button--normal{ font-size: 16px; height: 38px; width: 140px; }
		.result_warp{ padding: 5px 0; }
		.result_item{ background-color: #FFF; border-radius: 10px; padding: 10px; }
		.van-button--mini{ padding: 0 12px; height: 20px; }
		.location{ background-color: red; }
	</style>
</head>
<body>
	<div v-cloak id="app">
		<div class="pageWarp">
			<div class="pageWarp" style="flex:1;height: 1px;">
				<div style="flex:1;height: 1px;">
					<template v-if="!hasCopy">
						<van-field
							v-model="content"
							rows="16"
							type="textarea"
							placeholder="请输入或者粘贴内容"
							show-word-limit
						/>
					</template>
					<div v-else style="height:100%;padding: 10px 16px;box-sizing: border-box;background-color: #FFF;">
						<div class="textareaDiv" v-html="content"></div>
					</div>
				</div>
				<div class="button_box">
					<van-button :disabled="!content" :loading="correctionIng" loading-text="校正中..." @click="correction" type="primary" round size="normal" :color="appTheme">{{hasCopy?'手动编辑':'一键校正'}}</van-button>
					<van-button :disabled="!content" @click="copy" type="primary" style="margin-left: 30px;" round size="normal" :color="appTheme">一键复制</van-button>
				</div>
			</div>
			<template v-if="hasCopy">
				<div style="font-size:14px;font-weight: 700;color:#101010;padding:16px 12px 5px;">校正结果({{checklist.filter((obj)=>{ return !obj.replace; }).length}})</div>
				<div style="flex:1;height: 1px;overflow-y: auto;padding: 0 12px;">
					<div v-for="(item,index) in checklist" class="result_warp" :style="`opacity: ${item.replace?'0.6':'1'};`" @click="location(item)">
						<div class="result_item">
							<div style="display: flex;flex-flow: row;">
								<div style="flex:1;font-weight: 700;font-size: 14px;">{{(item.type||{}).name}}</div>
								<van-button v-if="item.suggest.length > 0" @click.stop="replaceText(item)" :disabled="!hasCopy" round plain hairline size="mini" :color="appTheme">{{item.replace?'还原':'替换'}}</van-button>
							</div>
							<div :style="`font-size: 14px;margin-top:10px;color:${appTheme}`">
								<!-- <svg t="1735205584369" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6740" width="14" height="14" style="vertical-align: middle;"><path d="M555.989333 171.221333v573.696c0 0.085333 0.213333 0.170667 0.213334 0.085334l208-216.405334a43.946667 43.946667 0 1 1 63.402666 61.013334l-283.093333 294.570666a44.032 44.032 0 0 1-63.402667 0L196.266667 587.989333a43.818667 43.818667 0 0 1 1.194666-62.208 44.032 44.032 0 0 1 62.208 1.237334l208 216.362666c0.085333 0.128 0.170667 0 0.170667-0.085333V170.24c0-23.381333 18.346667-42.496 41.301333-43.904 25.6-1.493333 46.805333 19.413333 46.805334 44.885333z" :fill="appTheme" p-id="6741"></path></svg> -->
								<span>{{item.word}}</span>
							</div>
							<div style="font-size: 14px;margin-top:10px;">{{(item.suggest||[]).join('、') || item.explanation}}</div>
						</div>
					</div>
				</div>
			</template>
		</div>
	</div>
</body>
<script type="text/javascript" src="../script/vue.min.js"></script>
<script type="text/javascript" src="../script/vant.min.js"></script>
<script type="text/javascript" src="../script/axios.min.js"></script>
<script type="text/javascript" src="../script/url.js"></script>
<script type="text/javascript" src="../script/api.js"></script>
<script type="text/javascript">
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var vm,pageParam={};
	var vmData = {
		appUrl:appUrl,
		appTheme:datas.appTheme||"#3657C0",
		token:datas.token,
		areaId:datas.areaId,
		content:"",
		correctionIng:false,
		hasCopy:false,//是否能复制

		checklist:[],
	};
	var methods = {
		init:function(){
			var that = this;
		},
		convertRichText(value){
			var textList = value.split('\n');
			var str = '';
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i];
				if (addText) {
					str += '<p>' + addText + '</p>';
				}
			}
			return str;
		},
		convertTextarea(value){
			var result = value.replace(/<br\s*\/?>/gi, '\n');
			result = result.replace(/<\/?p>/gi, '\n');
			result = result.replace(/<\/?div>/gi, '\n');
			result = result.replace(/\n+/g, '\n');
			result = result.replace(/<[^>]*>/g, '');
			result = result.trim();
			return result;
		},
		correction(){
			var that = this;
			if(that.hasCopy){
				that.content = that.convertTextarea(that.content);
				that.hasCopy = false;
				return;
			}
			that.correctionIng = true;
			axios.post(
				`${that.appUrl}summoner/open/typingVerification`,
				{
					text:that.convertRichText(that.content),
				},
				{
					headers: {
						"u-login-areaId": that.areaId,
						"content-type":"application/json",
						"Authorization": that.token,
						"u-terminal":"APP",
					} 
				}
			).catch(function (err) {
				that.correctionIng = false;
				alert(JSON.stringify(err));
			}).then(function (ret) {
				ret = ret.data || {};
				that.correctionIng = false;
				if(ret.code == 200){
					var data = ret.data||{};
					that.content = data.replace_text;
					that.checklist = (data.checklist || []).map((obj,oIndex)=>{
						obj.replace = false;
						obj.index = oIndex;
						return obj;
					});
					that.hasCopy = true;
				}else{
					alert(ret.message || ret.data);
				}
			});
		},
		copy(){
			var that = this;
			var text = that.convertTextarea(that.content);
			if (false && navigator.clipboard) {
				navigator.clipboard.writeText(text).then(function() {
					vant.Toast("已复制");
				}).catch(function(err) {
					vant.Toast("复制失败:", err);
				});
			} else {
				const textArea = document.createElement('textarea');
				textArea.value = text;
				document.body.appendChild(textArea);
				textArea.select();
				try {
					const successful = document.execCommand('copy');
					if (successful) {
						vant.Toast("已复制");
					} else {
						vant.Toast("复制失败:");
					}
				} catch (err) {
					console.error("执行复制时发生错误:", err);
				}
				document.body.removeChild(textArea);
			}
		},
		location(_item){
			var that = this;
			var datas = $api.domAll(".jdt_umold");
			if(_item.index >= datas.length){
				return;
			}
			datas.forEach((obj)=>{
				$api.removeCls(obj,"location");
			});
			var el = datas[_item.index];
			//滚动到界面内
			el.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
			//界面强提示
			that.locationStyle(_item.index,el,0);
		},
		locationStyle(_task,_dom,_index){
			var that = this;
			if($api.hasCls(_dom,"location")){
				$api.removeCls(_dom,"location");
			}else{
				$api.addCls(_dom,"location");
			}
			if(!_index){
				clearTimeout(window.locationTask);
			}
			_index++;
			if(_index > 3)
				return;
			window.locationTask = setTimeout(() => {
				that.locationStyle(_task,_dom,_index);
			}, 250);
		},
		replaceText(_item){
			var that = this;
			_item.replace = !_item.replace;
			that.checklist.sort((a, b) => {
				if (a.replace === b.replace) {
					return a.index - b.index;
				}
				return !a.replace ? -1 : 1;
			});
			var datas = that.getDatas();
			if(_item.index >= datas.length){
				return;
			}
			var data = datas[_item.index];
			if(_item.replace){
				_item.oldStyle = data.style;
			}
			that.content = that.content.replace(data.originalTag, `<span class="jdt_umold" style="${_item.replace?'':_item.oldStyle}">${_item.replace?_item.suggest[0]:_item.word}</span>`);
			setTimeout(() => {
				that.location(_item);
			}, 0);
		},
		getDatas(){
			var that = this;
			var regex = /<span class="jdt_umold"[^>]*style="([^"]*)"[^>]*>(.*?)<\/span>/g;
			var matches,result = [];
			while ((matches = regex.exec(that.content)) !== null) {
				result.push({ style: matches[1], content: matches[2],originalTag:matches[0] });
			}
			return result;
		}
	};

	window.onload = function() {
		showVue();
	};
	
	function showVue(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
</script>
</html>