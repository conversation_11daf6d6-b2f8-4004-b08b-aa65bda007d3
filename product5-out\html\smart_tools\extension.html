<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>智能扩写</title>
	<link rel="stylesheet" href="../css/vant.css" />
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;background-color: #F8FAFB;overflow-y: auto; }
		.pageWarp{ display: flex; flex-direction: column; width: 100%; height: 100%; }
		.van-field__body,.van-cell{height: 100%;}
		textarea {font-size: 14px;line-height: 24px;height:100%;color: #333;}
		.textareaDiv {font-size: 14px;height:100%;overflow-y: auto;color: #333;}
		p{ line-height: 24px;margin-block-start: 0; margin-block-end: 0; }
		.button_box{ display: flex; align-items: center;justify-content: center; padding: 16px; background-color: #FFF; }
		.van-button--normal{ font-size: 16px; height: 38px; width: 140px; }
		.result_warp{ padding: 5px 0; }
		.result_item{ background-color: #FFF; border-radius: 10px; padding: 10px; }
		.van-button--mini{ padding: 0 12px; height: 20px; }

		.numBox{
			width: 80px;
			height: 30px;
			border:1px solid #ccc;
			border-radius: 5px;
			text-align: center;
		}

		.van-field__body{
			height: 98%;
		}
	</style>
</head>
<body>
	<div v-cloak id="app">
		<div class="pageWarp">
			<div class="pageWarp" style="flex:1;height: 1px;">
				<div style="flex:1;height: 1px;">
					<template v-if="!hasCopy">
						<van-field
							v-model="content"
							rows="16"
							type="textarea"
							placeholder="请输入或者粘贴内容(原文超出1500字，不再支持扩写)"
							show-word-limit
						/>
					</template>
					<div v-else style="height:100%;padding: 10px 16px;box-sizing: border-box;background-color: #FFF;">
						<div class="textareaDiv" v-html="content"></div>
					</div>
				</div>
				<div style="background: #FFF;font-size: 14px;text-align: right;padding: 10px 16px;color:rgba(34,47,62,.7);">{{contentLength}}个字</div>
				<div class="button_box">
					<template v-if="!hasCopy">
						<div style="width: 140px;">
							<input disabled v-model="number" class="numBox"/>
						</div>
						<van-button :disabled="!content" :loading="correctionIng" style="margin-left: 30px;" loading-text="扩写中..." @click="correction" type="primary" round size="normal" :color="appTheme">一键扩写</van-button>
					</template>
					<template v-else>
						<van-button @click="correction" type="primary" round size="normal" :color="appTheme">重新编辑</van-button>
						<van-button style="margin-left: 30px;" @click="copy" type="primary"  round size="normal" :color="appTheme">一键复制</van-button>
					</template>
				</div>
			</div>
			<template v-if="hasCopy">
				<div style="font-size:14px;font-weight: 700;color:#101010;padding:16px 12px 5px;">扩写结果</div>
				<div id="result" style="flex:1;height: 1px;overflow-y: auto;padding: 0 12px;">
					<div class="result_warp">
						<div class="result_item">
							<div :style="`font-size: 14px;color:${appTheme}`" v-html="result"></div>
						</div>
					</div>
				</div>
			</template>
		</div>
	</div>
</body>
<script type="text/javascript" src="../script/vue.min.js"></script>
<script type="text/javascript" src="../script/vant.min.js"></script>
<script type="text/javascript" src="../script/axios.min.js"></script>
<script type="text/javascript" src="../script/url.js"></script>
<script type="text/javascript" src="../script/api.js"></script>
<script type="text/javascript" src="../script/jquery-3.6.4.min.js"></script>
<script type="text/javascript">
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var vm,pageParam={};
	var vmData = {
		appUrl:appUrl,
		appTheme:datas.appTheme||"#3657C0",
		token:datas.token,
		areaId:datas.areaId,
		number:"1500",
		content:"",
		correctionIng:false,
		hasCopy:false,//是否能复制

		result:"",
		contentLength:0,
	};
	var vmWatch = {
		result(_value){
			var that = this;
			if(_value){
				setTimeout(() => {
					function stop(){
						window.dotScroll = true;
						$("#result").stop();
						clearTimeout(window.dotTask);
						window.dotTask = setTimeout(() => {
							window.dotScroll = false;
						}, 2000);
					}
					$("#result").on("touchstart", stop);
					$("#result").on("touchmove", stop);
					$("#result").on("touchend", stop);
					$("#result").on("click", stop);
					$("#result").on("wheel", stop);
				}, 0);
			}
		},
		content(_value){
			this.contentLength = this.fnGetCpmisWords(_value);
		}
	};
	var methods = {
		//用word方式计算正文字数
		fnGetCpmisWords: function(str){
			var sLen = 0;
				try{
				//先将回车换行符做特殊处理
				str = str.replace(/(\r\n+|\s+|　+)/g,"龘");
				//处理英文字符数字，连续字母、数字、英文符号视为一个单词
				str = str.replace(/[\x00-\xff]/g,"m"); 
				//合并字符m，连续字母、数字、英文符号视为一个单词
				str = str.replace(/m+/g,"*");
					//去掉回车换行符
				str = str.replace(/龘+/g,"");
				//返回字数
				sLen = str.length;
			}catch(e){
			
			}
			return sLen;
		},
		init:function(){
			var that = this;
		},
		convertRichText(value){
			var textList = value.split('\n');
			var str = '';
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i];
				if (addText) {
					str += '<p>' + addText + '</p>';
				}
			}
			return str;
		},
		convertTextarea(value){
			var result = value.replace(/<br\s*\/?>/gi, '\n');
			result = result.replace(/<\/?p>/gi, '\n');
			result = result.replace(/<\/?div>/gi, '\n');
			result = result.replace(/\n+/g, '\n');
			result = result.replace(/<[^>]*>/g, '');
			result = result.trim();
			return result;
		},
		correction(){
			var that = this;
			if(window.controller){
				try{
					window.controller.abort();
				}catch(e){
					console.log(e);
				}
			}
			if(that.hasCopy){
				that.content = that.convertTextarea(that.content);
				that.correctionIng = false;
				that.hasCopy = false;
				return;
			}
			if(that.contentLength > 1500){
				vant.Toast("原文已超1500字，不再支持扩写");
				return;
			}
			that.correctionIng = true;
			that.result = "";
			window.showText = "";
			window.isTask = false;
			window.controller = new AbortController();
			fetch(
				`${that.appUrl}chat/intelligentStream`,
				{
					method: 'POST',
					headers: {
						'Accept': 'text/event-stream',
						'Content-Type': 'application/json',
						"u-login-areaId": that.areaId,
						"Authorization": that.token,
						"u-terminal":"APP",
					},
					body: JSON.stringify({
						content:that.convertRichText(that.content),
						bot_type:3,
						wordNumber:that.number
					}),
					signal: window.controller.signal,  // 传递 signal
				}
			).catch(function (err) {
				that.correctionIng = false;
				console.log("abc:"+err.name);
				that.result = err.name == "TypeError"?"网络异常":" ";
				that.goButtom();
			}).then(function (response) {
				if(!response)return;
				const reader = response.body.getReader();  // 获取流的读取器
				const decoder = new TextDecoder();  // 用于解码字节数据
				let buffer = '';
				function showTextTask(){
					if(window.showText.length){
						window.isTask = true;
						let firstChar = window.showText.charAt(0);
						that.result += firstChar;
						that.result = that.result.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/\n/g, '<br />');
						that.goButtom();
						window.showText = window.showText.slice(1);
						setTimeout(() => {
							showTextTask();
						}, 30);
					}else{
						window.isTask = false;
					}
				}
				that.hasCopy = true;
				// 逐步处理流数据
				reader.read().then(function processText({ done, value }) {
					if (done) {
						that.correctionIng = false;
						return;
					}
					buffer += decoder.decode(value, { stream: true });
					// 根据换行符分割事件数据
					const lines = buffer.split('\n\n');
					if(lines.length == 1 && lines[0].startsWith('{')){
						var data = JSON.parse(lines[0]);
						window.showText += data.message || data.data;
						if(!window.isTask){
							showTextTask();
						}
					}
					for (let i = 0; i < lines.length - 1; i++) {
						const line = lines[i];
						if (line.startsWith('data:')) {
							try{
								const eventData = JSON.parse(line.substring(5).trim() || "{}");
								if(eventData.event == "conversation.message.delta"){
									window.showText += eventData.data.content;
									if(!window.isTask){
										showTextTask();
									}
								}
							}catch(e){
								console.log(e)
							}
						}
					}
					buffer = lines[lines.length - 1];
					reader.read().then(processText);
				});
			});
		},
		copy(){
			var that = this;
			var text = that.convertTextarea(that.result);
			if (false && navigator.clipboard) {
				navigator.clipboard.writeText(text).then(function() {
					vant.Toast("已复制");
				}).catch(function(err) {
					vant.Toast("复制失败:", err);
				});
			} else {
				const textArea = document.createElement('textarea');
				textArea.value = text;
				document.body.appendChild(textArea);
				textArea.select();
				try {
					const successful = document.execCommand('copy');
					if (successful) {
						vant.Toast("已复制");
					} else {
						vant.Toast("复制失败:");
					}
				} catch (err) {
					console.error("执行复制时发生错误:", err);
				}
				document.body.removeChild(textArea);
			}
		},
		goButtom(){
			try{
				if($("#result") && !window.dotScroll){
					$("#result").stop();
					$("#result").animate({scrollTop:document.getElementById("result").scrollHeight},200);
				}
			}catch(e){

			}
		},
	};

	window.onload = function() {
		showVue();
	};
	
	function showVue(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			watch:vmWatch,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
</script>
</html>