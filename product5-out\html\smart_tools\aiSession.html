<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>AI小助手</title>
	<link rel="stylesheet" href="../css/<EMAIL>" />
	<link rel="stylesheet" href="./css/dsmarkdown.css" />
	<style type="text/css">
		html,page,:root,:host{ --van-primary-color:transparent; --van-primary-color-transparent:transparent;--van-width-page:800px; }
		[v-cloak] { display: none; }
		.click{ cursor: pointer; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;min-width: 360px; }
		.buttons{display: flex;cursor: pointer;}
		/* #app{ flex-direction: column;justify-content: center; align-items: center;  display: flex; position: absolute; top: 0; bottom: 0; left: 0; right: 0; overflow: hidden; } */
		.ai_box{ width: 100%; max-width: var(--van-width-page);height: 100%; display: flex; flex-direction: column; flex-grow: 1; flex-shrink: 0;margin: auto; }
		.ai_header{ box-sizing: border-box; flex-grow: 0; flex-shrink: 0;  height: 56px; padding-top: 10px; display: flex;align-items: center; position: relative; }
		.ai_header_icon{padding: 10px;margin:0 6px;cursor: pointer;}
		.ai_header_text{ flex:1; text-align: center; text-overflow: ellipsis;white-space: nowrap;overflow: hidden;display:inline;font-weight: 600; font-size: 16px; color: #333333; }
		.ai_body{ flex-grow: 1; position: relative; display: flex; flex-direction: column; flex-grow: 1; height: 100%; position: relative; }
		.ai_box_warp{ padding: 0 16px; min-height: 100%; position: absolute; top: 0; bottom: 0; left: 0; right: 0; overflow: auto; } 
		.session_box{ flex-direction: column; flex-grow: 1; height: 100%; display: flex; position: relative; }
		.session_items_box{ width: 100%; box-sizing: border-box; flex-grow: 1; margin: auto;padding-bottom: 20px; }
		.session_input_box{ z-index: 1; flex-direction: column; align-items: center; margin-top: auto; display: flex; position: -webkit-sticky; position: sticky; bottom: 0;background: #FFF; }
		.session_input{ flex-grow: 1; width: 100%; position: relative; }
		.session_input_warp{ z-index: 1; flex-direction: column; justify-content: flex-start; align-items: flex-start; padding: 10px; display: flex; overflow: hidden; background-color: rgb(243 244 246); box-shadow: 0px 0px 0px .5px #dce0e9; border-radius: 24px; cursor: text; box-sizing: border-box; width: 100%; font-size: 16px; line-height: 28px; }
		.session_input_hint{ font-size: 12px; color: rgb(163 163 163); margin: 6px 0; line-height: 14px; }
		.ai_input_box{ max-height: 336px; width: 100%; margin: 0 4px; position: relative; }
		.ai_input,.ai_input_text{ font-size: inherit; line-height: inherit; word-break: break-word; white-space: pre-wrap; border: none; width: 100%; margin: 0; padding: 0; font-family: inherit; display: block; top: 0; bottom: 0; left: 0; right: 0; overflow: auto; }
		.ai_input{ resize: none; color: rgb(64 64 64); caret-color: rgb(64 64 64); background-color: transparent;position: absolute; }
		.ai_input::placeholder { color: rgba(13,13,13,.3) }
		.ai_input_text{ visibility: hidden; pointer-events: none; min-height: 56px; }
		.ai_input_option_box{ width: calc(100% - 2px); padding-left: 2px; flex-wrap: wrap; align-items: center; margin-top: 4px; display: flex; }
		.ai_button{ position: relative; text-decoration: none; outline: none; font-variant-numeric: tabular-nums; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; cursor: pointer; white-space: nowrap; box-sizing: border-box; border: 1px solid #E0E4ED; border-radius: 14px; display: flex; justify-content: center; align-items: center; height: 28px; margin-right: 10px; padding: 0 7px; background: #FFF;color: #4c4c4c; }
		.ai_button_active{ background: var(--van-primary-color); color: #FFF; opacity: 0.9; }
		.ai_button_icon{ line-height: 0; display: inline-flex; margin-right: 4px; color: inherit; }
		.ai_button_text{ font-size: 12px; line-height: 17px; color: inherit; }
		.ai_input_option_right{ flex: 1; justify-content: flex-end; align-items: center; display: flex; }
		.ai_button_file{ color: #4c4c4c; cursor: pointer; border-radius: 10px; flex-shrink: 0; justify-content: center; align-items: center; width: 32px; height: 32px; margin-top: auto; margin-right: 10px; display: flex; }
		.ai_button_send{ white-space: nowrap; cursor: pointer; color: #fff; background: var(--van-primary-color); border: none; border-radius: 16px; flex-direction: column; flex-shrink: 0; justify-content: center; align-items: center; min-width: 32px; height: 32px; margin-top: auto; display: flex; }
		.ai_button_send .van-loading__text{ margin-left:0; }

		.hint_box{ padding:20px 0; display: flex; align-items: flex-start; }
		.hint_box .session_items_icon{ width: 50px; height: 50px;margin-right: 20px; }
		.hint_box_title{ font-weight: 600; font-size: 18px; color: #333333; line-height: 20px; }
		.hint_box_summary{ font-weight: 400; font-size: 16px; color: #333333; line-height: 20px; margin-top:10px; }
		.session_items_icon{ width: 30px; height: 30px;margin-right: 8px; }
		.hint_question_hint{ font-size: 16px;color: #666666; }
		.hint_question_text{ font-size: 16px; color: #333333; background: rgba(143,158,178,0.08); border-radius: 6px 6px 6px 6px; padding: 8px 10px;margin-top: 10px; }
		.hint_question_text span{ margin-right: 7px; color: var(--van-primary-color); }

		.history_box{ width: 100%; height: 100%; overflow: hidden; display: flex; flex-direction: column; }
		.history_header{ display: block; padding: 0; border: 0; }
		.chat_new_box{ display: flex; align-items: center; margin: 8px 20px; }
		.chat_new_warp{ color: rgba(0, 0, 0, 0.9); border-radius: 8px; background: #F3F3F3; cursor: pointer; text-align: center; flex: 1; display: flex; align-items: center; justify-content: center; }
		.chat_new_warp span{font-size: 14px; line-height: 36px; font-weight: 500;margin-left:10px;}
		.history_body{overflow: auto; -webkit-box-flex: 1; -ms-flex: 1; flex: 1;}
		.history_time{ position: sticky; top: 0; background: #FFF; color: rgba(0, 0, 0, 0.4); font-weight: 500; font-size: 14px; height: 32px; line-height: 32px; z-index: 1; padding: 0 20px 2px; margin-top: 16px; margin-right: 12px; }
		.history_item_warp{ position: relative; cursor: pointer; display: flex; align-items: center; border-radius: 8px; margin: 0 12px 2px; }
		.history_item_warp.active{ background:#F5F5F5; }
		.history_item{ flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; padding: 9px 8px; font-size: 14px; line-height: 22px; color: #181818; }
		.history_item_warp .van-popover__wrapper{ display: flex; }

		.session_items_send_box{flex:1;display: flex;flex-direction: column;overflow: auto;}
		.session_items_send{ font-size: 16px; line-height: 28px; color: #262626; padding: 8px 20px; box-sizing: border-box;  word-break: break-word; background-color: var(--van-primary-color-transparent);border-radius: 12px 2px 12px 12px; position: relative; }
		.session_items_receive{ word-wrap: break-word; box-sizing: border-box;max-width: 100%; }
		.session_items_think{ user-select: none; border-radius: 10px; justify-content: center; align-items: center; width: fit-content; padding: 7px 14px; display: flex; color: rgb(38 38 38); margin-bottom: 12px; font-size: 12px; line-height: 18px; background: rgb(245, 245, 245); cursor: pointer; }
		.session_items_think span{margin: 0 6px;}
		.session_items_think_text{ color: #8b8b8b;  margin-bottom: 13px; padding: 0 0 0 13px; line-height: 26px; position: relative; border-left: 2px solid #e5e5e5; }
		.session_items_think_text>:first-child { margin-top: 0 !important; }
		.session_items_think_text>:last-child { margin-bottom: 0 !important; }
		.session_items_receive>:first-child { margin-top: 0 !important; }
		.session_items_receive>:last-child { margin-bottom: 0 !important; }
		.session_items_association{ font-size: 14px; padding: 5px 10px; background: var(--van-primary-color-transparent); border-radius: 5px;color: #444; }

		.session_items_option_box{ display: flex; margin-top: 12px; height: 20px; align-items: center; gap: 12px; color:#909090; cursor: pointer; opacity: 1; transition: opacity 0.2s ease-in-out; }
		.session_items_send_warp:hover .session_items_option_box{ opacity: 1; }

		.session_items_record_box{ padding: 15px; background: #f5f5f5; border-radius: 6px; width: 100%; box-sizing: border-box; }
		.session_items_record_header{ display: flex; align-items: center; }
		.record_header_title{ font-weight: 600; font-size: 16px; color: #333333; flex:1; padding: 0 7px; text-overflow: ellipsis;white-space: nowrap;overflow: hidden;display:inline; }
		.record_header_time{ font-size: 12px; color: #666666; padding: 0 7px; }
		/* 定义上下波动动画 */
		@keyframes wave { 0%, 100% { transform: scaleY(1) } 50% { transform: scaleY(0.5) } }
		.recordIng {animation: wave 1.5s ease-in-out infinite; }
		.session_items_record_header_add{margin-top:10px; font-weight: 400; font-size: 12px; color: #666666;}
		.session_items_record_content{ margin-top:15px; font-size: 16px; color: #333333;max-height: 100px; overflow: auto; }
		.session_items_record_btn{ margin-top:15px; font-size: 16px; color: #333333; background: #FFFFFF; box-shadow: 0px 2px 8px 0px rgba(143,158,178,0.1); border-radius: 4px; padding: 7px 15px; text-align: center; }
		.maxPopup{display: flex;}
		.dialogs .van-field__body{background: #f5f5f5;border-radius: 8px;padding:15px 10px;}
		.session_input_arrow{ position: absolute; top: -64px; left: 50%; width: 40px; height: 40px; margin: 0 0 0 -24px; padding: 0; display: flex; align-items: center; justify-content: center; background: #fff; border-radius: 100%; border: 0; box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.2); transition: 0.2s; overflow: hidden; z-index: 1; cursor: pointer;}
	</style>
</head>
<body>
	<div v-cloak id="app">
		<div class="ai_box">
			<!-- 顶部标题栏 -->
			<div class="ai_header">
				<svg @click="showSessionHistory = !showSessionHistory;" width="20" height="20" viewBox="0 0 20 20" class="ai_header_icon" fill="none" xmlns="http://www.w3.org/2000/svg"> <path id="Vector" d="M15.8333 2.5H4.16667C3.24619 2.5 2.5 3.24619 2.5 4.16667V15.8333C2.5 16.7538 3.24619 17.5 4.16667 17.5H15.8333C16.7538 17.5 17.5 16.7538 17.5 15.8333V4.16667C17.5 3.24619 16.7538 2.5 15.8333 2.5Z" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path id="Vector_2" d="M2.5 7.5H17.5" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path id="Vector_3" d="M7.5 17.5V7.5" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>
				<div class="ai_header_text">{{title || '新对话'}}</div>
				<svg @click="newTask()" width="20" height="20" viewBox="0 0 20 20" class="ai_header_icon" fill="none" xmlns="http://www.w3.org/2000/svg"> <path id="Vector" d="M1.5 3.66666C1.5 2.56209 2.39543 1.66666 3.5 1.66666H16.5C17.6046 1.66666 18.5 2.56209 18.5 3.66666V13C18.5 14.1046 17.6046 15 16.5 15H14.3333V18.3333L10 15H5.98883C5.22551 15 4.46921 14.8543 3.76048 14.5709L2.75722 14.1696C1.9979 13.8658 1.5 13.1304 1.5 12.3126V3.66666Z" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path id="Vector_2" d="M10 5V11" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path id="Vector_3" d="M7 8H13" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <defs> <clipPath id="clip0_13_513"> <rect width="20" height="20" fill="white"/> </clipPath> </defs> </svg>
			</div>
			<!-- 主体框架 -->
			<div class="ai_body">
				<div id="assistant" class="ai_box_warp">
					<div class="session_box">
						<!-- 会话列表 -->
						<div class="session_items_box">
							<template v-if="initInfo.title">
								<div class="hint_box">
									<img class="session_items_icon" :src="aiIcon" />
									<div>
										<div class="hint_box_title">{{initInfo.title}}</div>
										<div class="hint_box_summary">{{initInfo.summary}}</div>
									</div>
								</div>
								<div v-if="initInfo.questions.length">
									<div class="hint_question_hint">您可以试着问我：</div>
									<div v-for="item in initInfo.questions" @click="sendMessage(item)" class="hint_question_text click"><span>#</span>{{item}}</div>
								</div>
							</template>
							<template v-if="listData.length">
								<div v-for="(item,_index) in listData" :id="item.type+'_'+item.udid" class="session_items_send_warp" :style="`margin-top:${item.type=='association'?10:20}px;display: flex;flex-flow: ${item.type=='send'?'row-reverse':'row'};`">
									<div v-if="item.type!='send' && !item.recordState" style="width:40px;height: 25px;" >
										<img v-if="item.type=='receive'" style="height: 28px;" :src="aiIcon" />
									</div>
									<div class="session_items_send_box" :style="`max-width: calc(100% - ${item.type=='send'?5:0}0px);align-items: flex-${item.type=='send'?'end':'start'};`">
										<!-- 头像 -->
										<template v-if="item.type=='send'">
											<div class="session_items_send" v-html="item.text"></div>
										</template>
										<template v-else-if="item.type=='receive'">
											<!-- 思考按钮 -->
											<div v-if="item.think" @click="item.showThink = !item.showThink;" class="session_items_think">
												<svg width="12" height="12" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.656 17.344c-1.016-1.015-1.15-2.75-.313-4.925.325-.825.73-1.617 1.205-2.365L3.582 10l-.033-.054c-.5-.799-.91-1.596-1.206-2.365-.836-2.175-.703-3.91.313-4.926.56-.56 1.364-.86 2.335-.86 1.425 0 3.168.636 4.957 1.756l.053.034.053-.034c1.79-1.12 3.532-1.757 4.957-1.757.972 0 1.776.3 2.335.86 1.014 1.015 1.148 2.752.312 4.926a13.892 13.892 0 0 1-1.206 2.365l-.034.054.034.053c.5.8.91 1.596 1.205 2.365.837 2.175.704 3.911-.311 4.926-.56.56-1.364.861-2.335.861-1.425 0-3.168-.637-4.957-1.757L10 16.415l-.053.033c-1.79 1.12-3.532 1.757-4.957 1.757-.972 0-1.776-.3-2.335-.86zm13.631-4.399c-.187-.488-.429-.988-.71-1.492l-.075-.132-.092.12a22.075 22.075 0 0 1-3.968 3.968l-.12.093.132.074c1.308.734 2.559 1.162 3.556 1.162.563 0 1.006-.138 1.298-.43.3-.3.436-.774.428-1.346-.008-.575-.159-1.264-.449-2.017zm-6.345 1.65l.058.042.058-.042a19.881 19.881 0 0 0 4.551-4.537l.043-.058-.043-.058a20.123 20.123 0 0 0-2.093-2.458 19.732 19.732 0 0 0-2.458-2.08L10 5.364l-.058.042A19.883 19.883 0 0 0 5.39 9.942L5.348 10l.042.059c.631.874 1.332 1.695 2.094 2.457a19.74 19.74 0 0 0 2.458 2.08zm6.366-10.902c-.293-.293-.736-.431-1.298-.431-.998 0-2.248.429-3.556 1.163l-.132.074.12.092a21.938 21.938 0 0 1 3.968 3.968l.092.12.074-.132c.282-.504.524-1.004.711-1.492.29-.753.442-1.442.45-2.017.007-.572-.129-1.045-.429-1.345zM3.712 7.055c.202.514.44 1.013.712 1.493l.074.13.092-.119a21.94 21.94 0 0 1 3.968-3.968l.12-.092-.132-.074C7.238 3.69 5.987 3.262 4.99 3.262c-.563 0-1.006.138-1.298.43-.3.301-.436.774-.428 1.346.007.575.159 1.264.448 2.017zm0 5.89c-.29.753-.44 1.442-.448 2.017-.008.572.127 1.045.428 1.345.293.293.736.431 1.298.431.997 0 2.247-.428 3.556-1.162l.131-.074-.12-.093a21.94 21.94 0 0 1-3.967-3.968l-.093-.12-.074.132a11.712 11.712 0 0 0-.71 1.492z" fill="currentColor" stroke="currentColor" stroke-width=".1"></path><path d="M10.706 11.704A1.843 1.843 0 0 1 8.155 10a1.845 1.845 0 1 1 2.551 1.704z" fill="currentColor" stroke="currentColor" stroke-width=".2"></path></svg>
												<span>{{item.think}}</span>
												<svg width="10" height="6" :style="`transform: rotate(${item.showThink?'18':''}0deg);`" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.786 4.167L2.765 1.259c-.416-.4-.985-.482-1.273-.183-.287.298-.183.864.233 1.264l3.021 2.908c.416.4.986.482 1.273.184.287-.299.183-.865-.233-1.265z" fill="currentColor"></path><path d="M8.197 1.206L5.288 4.208c-.4.413-.484.982-.187 1.27.298.289.864.187 1.265-.227L9.274 2.25c.401-.414.485-.983.187-1.271-.297-.288-.863-.187-1.264.227z" fill="currentColor"></path></svg>
											</div>
											<!-- 思考数据 -->
											<div v-if="item.think && item.showThink && item.thinkText" class="session_items_think_text" v-html="item.thinkText"></div>
											<!-- 录音纪要box -->
											<div v-if="item.recordState" class="session_items_record_box">
												<div class="session_items_record_header">
													<svg width="18" height="18" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16840" width="200" height="200"><path d="M511.997442 702.435237c122.710786 0 222.174102-99.428523 222.174102-222.171032 0-5.458315-1.211595-10.541077-1.614777-15.869433 0.465604-5.332449 1.614777-10.419304 1.614777-15.869433L734.171544 321.56988c0-5.458315-1.149173-10.540054-1.614777-15.868409 0.404206-5.336542 1.614777-10.41828 1.614777-15.870456 0-122.740462-99.463316-222.174102-222.174102-222.174102-122.706693 0-222.171032 99.43364-222.171032 222.174102 0 5.452175 1.211595 10.533914 1.611707 15.870456-0.465604 5.328355-1.611707 10.410094-1.611707 15.868409l0 126.95546c0 5.450129 1.146103 10.536984 1.611707 15.869433-0.400113 5.328355-1.611707 10.411117-1.611707 15.869433C289.82641 603.006713 389.290749 702.435237 511.997442 702.435237zM354.729605 300.926724c-0.249687-3.472078-0.680499-6.881734-1.425466-11.095709 0-87.530542 71.195505-158.693302 158.693302-158.693302 87.499843 0 158.693302 71.16276 158.415986 159.560042-0.463558 3.347235-0.898463 6.757914-1.14508 10.228969-0.249687 3.406586-0.249687 6.881734 0.062422 10.287297 0.245593 3.037173 0.649799 6.012947 1.360997 10.355859l-0.308015 128.254036c-0.404206 3.038196-0.807389 6.013971-1.052982 9.05012-0.312108 3.413749-0.312108 6.881734-0.062422 10.295484 0.246617 3.467985 0.681522 6.877641 1.423419 11.094686 0 87.528496-71.193459 158.694325-158.693302 158.694325-87.496773 0-158.693302-71.16583-158.412916-159.564136 0.464581-3.347235 0.895393-6.757914 1.14508-10.224876 0.249687-3.413749 0.249687-6.881734-0.061398-10.295484-0.25071-3.036149-0.649799-6.011924-1.364067-10.348696l0.311085-128.257106c0.404206-3.041266 0.803295-6.01704 1.054005-9.054213C354.980315 307.808458 354.980315 304.33331 354.729605 300.926724z" p-id="16841"></path><path d="M861.127004 512.002047l-63.47773 0c0 140.218532-113.692389 253.909897-253.912967 253.909897l-63.476707 0c-140.220578 0-253.909897-113.691365-253.909897-253.909897l-63.476707 0c0 175.306678 142.112672 317.386604 317.386604 317.386604l0 63.476707L289.82641 892.865357l0 63.476707 190.432167 0 63.476707 0 190.43626 0 0-63.476707L543.736307 892.865357l0-63.476707C719.013309 829.38865 861.127004 687.309748 861.127004 512.002047z" p-id="16842"></path></svg>
													<span class="record_header_title">{{item.title}}</span>
													<span v-if="item.recordState!=4" class="record_header_time">{{formatTime(item.recordTime,true)}}</span>
													<svg v-if="item.recordState==2" width="18" height="18" class="recordIng" viewBox="0 0 1280 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25205" width="200" height="200" fill="#8b8b8b"><path d="M544 112A48 48 0 0 1 592 160v704a48 48 0 0 1-96 0V160A48 48 0 0 1 544 112zM736 368a48 48 0 0 1 48 48v320a48 48 0 0 1-96 0V416a48 48 0 0 1 48-48zM928 304a48 48 0 0 1 48 48v448a48 48 0 0 1-96 0V352A48 48 0 0 1 928 304zM352 304a48 48 0 0 1 48 48v448a48 48 0 0 1-96 0V352A48 48 0 0 1 352 304zM160 432A48 48 0 0 1 208 480v192a48 48 0 1 1-96 0v-192A48 48 0 0 1 160 432zM1120 400a48 48 0 0 1 48 48v224a48 48 0 1 1-96 0v-224a48 48 0 0 1 48-48z" p-id="25206"></path></svg>
												</div>
												<div v-if="item.recordState==4" class="session_items_record_header_add">{{item.time}}  {{formatTime(item.recordTime,true)}}</div>
												<div class="session_items_record_content" v-html="(item.recordState==2?item.recordData.map(obj => obj.text).join('') + item.recordCache:'') || item.showText || item.text"></div>
												<div @click="recordBtn(item)" :style="`${item.recordState==3?'opacity: 0.4;':item.recordState==2?'color:'+appTheme+';':''};`" class="session_items_record_btn">{{item.btnText}}</div>
											</div>
											<!-- 回答正文 -->
											<div v-else-if="item.text" class="session_items_receive ds-markdown" v-html="item.showText || item.text"></div>
											<div v-else>
												<van-loading :size="25"/><span>正在思考</span>
											</div>
										</template>
										<template v-if="item.type=='association'">
											<div @click="openAssociation(item)" class="session_items_association" v-html="item.text"></div>
										</template>
										<!-- 操作按钮区域 -->
										<div v-if="!item.dotOptions && item.type!='association'" class="session_items_option_box">
											<!-- 复制 -->
											<div @click="copy(item.text)" title="复制" class="buttons">
												<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clipPath id="clip1248_20193"><rect width="17.052675" height="17.052441" transform="translate(1.000000 1.000000)" fill="white" fill-opacity="0"></rect></clipPath><clipPath id="clip1257_20794"><rect id="复制" width="20.000000" height="20.000000" fill="white" fill-opacity="0"></rect></clipPath></defs><g clip-path="url(#clip1257_20794)"><g clip-path="url(#clip1248_20193)"><path id="path" d="M5.03 14.64C4.77 14.64 4.5 14.62 4.24 14.56C3.98 14.51 3.73 14.43 3.49 14.33C3.24 14.23 3.01 14.1 2.79 13.96C2.57 13.81 2.37 13.64 2.18 13.45C1.99 13.26 1.82 13.05 1.68 12.83C1.53 12.61 1.4 12.37 1.3 12.13C1.2 11.88 1.13 11.63 1.07 11.36C1.02 11.1 1 10.84 1 10.57L1 5.07C1 4.8 1.02 4.54 1.07 4.27C1.13 4.01 1.2 3.76 1.3 3.51C1.4 3.26 1.53 3.03 1.68 2.81C1.82 2.58 1.99 2.38 2.18 2.19C2.37 2 2.57 1.83 2.79 1.68C3.01 1.53 3.24 1.41 3.49 1.31C3.73 1.2 3.98 1.13 4.24 1.07C4.5 1.02 4.77 1 5.03 1L10.49 1C10.75 1 11.01 1.02 11.27 1.07C11.53 1.13 11.78 1.2 12.03 1.31C12.27 1.41 12.51 1.53 12.73 1.68C12.95 1.83 13.15 2 13.34 2.19C13.53 2.38 13.69 2.58 13.84 2.81C13.99 3.03 14.11 3.26 14.21 3.51C14.31 3.76 14.39 4.01 14.44 4.27C14.5 4.54 14.52 4.8 14.52 5.07L12.94 5.07C12.94 4.91 12.92 4.75 12.89 4.58C12.86 4.43 12.81 4.27 12.75 4.12C12.69 3.97 12.61 3.83 12.52 3.69C12.43 3.56 12.33 3.43 12.22 3.32C12.1 3.2 11.98 3.1 11.85 3.01C11.71 2.92 11.57 2.84 11.42 2.78C11.27 2.72 11.12 2.67 10.96 2.64C10.81 2.61 10.65 2.59 10.49 2.59L5.03 2.59C4.87 2.59 4.71 2.61 4.55 2.64C4.4 2.67 4.24 2.72 4.09 2.78C3.95 2.84 3.8 2.92 3.67 3.01C3.54 3.1 3.41 3.2 3.3 3.32C3.18 3.43 3.08 3.56 2.99 3.69C2.9 3.83 2.83 3.97 2.77 4.12C2.71 4.27 2.66 4.43 2.63 4.58C2.6 4.75 2.58 4.91 2.58 5.07L2.58 10.57C2.58 10.73 2.6 10.89 2.63 11.05C2.66 11.21 2.71 11.37 2.77 11.52C2.83 11.67 2.9 11.81 2.99 11.94C3.08 12.08 3.18 12.2 3.3 12.32C3.41 12.43 3.54 12.54 3.67 12.63C3.8 12.72 3.95 12.79 4.09 12.86C4.24 12.92 4.4 12.96 4.55 13C4.71 13.03 4.87 13.04 5.03 13.04L5.03 14.64Z" fill="currentColor" fill-opacity="1.000000" fill-rule="evenodd"></path></g><path id="path" d="M14.75 18.91L9.3 18.91C9.03 18.91 8.77 18.88 8.51 18.83C8.25 18.78 8 18.7 7.75 18.6C7.51 18.49 7.27 18.37 7.05 18.22C6.83 18.07 6.63 17.9 6.44 17.71C6.25 17.52 6.09 17.32 5.94 17.1C5.79 16.87 5.67 16.64 5.57 16.39C5.47 16.14 5.39 15.89 5.34 15.63C5.28 15.37 5.26 15.1 5.26 14.83L5.26 9.33C5.26 9.06 5.28 8.8 5.34 8.54C5.39 8.28 5.47 8.02 5.57 7.77C5.67 7.53 5.79 7.29 5.94 7.07C6.09 6.85 6.25 6.64 6.44 6.45C6.63 6.26 6.83 6.09 7.05 5.95C7.27 5.8 7.51 5.67 7.75 5.57C8 5.47 8.25 5.39 8.51 5.34C8.77 5.29 9.03 5.26 9.3 5.26L14.75 5.26C15.01 5.26 15.28 5.29 15.54 5.34C15.8 5.39 16.05 5.47 16.29 5.57C16.54 5.67 16.77 5.8 16.99 5.95C17.21 6.09 17.41 6.26 17.6 6.45C17.79 6.64 17.96 6.85 18.1 7.07C18.25 7.29 18.37 7.53 18.48 7.77C18.58 8.02 18.65 8.28 18.71 8.54C18.76 8.8 18.78 9.06 18.78 9.33L18.78 14.83C18.78 15.1 18.76 15.37 18.71 15.63C18.65 15.89 18.58 16.14 18.48 16.39C18.37 16.64 18.25 16.87 18.1 17.1C17.96 17.32 17.79 17.52 17.6 17.71C17.41 17.9 17.21 18.07 16.99 18.22C16.77 18.37 16.54 18.49 16.29 18.6C16.05 18.7 15.8 18.78 15.54 18.83C15.28 18.88 15.01 18.91 14.75 18.91ZM9.3 6.86C9.13 6.86 8.97 6.87 8.82 6.91C8.66 6.94 8.51 6.98 8.36 7.05C8.21 7.11 8.07 7.18 7.93 7.28C7.8 7.37 7.68 7.47 7.56 7.58C7.45 7.7 7.35 7.82 7.26 7.96C7.17 8.09 7.09 8.24 7.03 8.38C6.97 8.54 6.92 8.69 6.89 8.85C6.86 9.01 6.84 9.17 6.84 9.33L6.84 14.83C6.84 15 6.86 15.16 6.89 15.32C6.92 15.48 6.97 15.63 7.03 15.78C7.09 15.93 7.17 16.07 7.26 16.21C7.35 16.34 7.45 16.47 7.56 16.58C7.68 16.7 7.8 16.8 7.93 16.89C8.07 16.98 8.21 17.06 8.36 17.12C8.51 17.18 8.66 17.23 8.82 17.26C8.97 17.29 9.13 17.31 9.3 17.31L14.75 17.31C14.91 17.31 15.07 17.29 15.23 17.26C15.38 17.23 15.54 17.18 15.69 17.12C15.83 17.06 15.98 16.98 16.11 16.89C16.24 16.8 16.37 16.7 16.48 16.58C16.59 16.47 16.7 16.34 16.79 16.21C16.87 16.07 16.95 15.93 17.01 15.78C17.07 15.63 17.12 15.48 17.15 15.32C17.18 15.16 17.2 15 17.2 14.83L17.2 9.33C17.2 9.17 17.18 9.01 17.15 8.85C17.12 8.69 17.07 8.54 17.01 8.38C16.95 8.24 16.87 8.09 16.79 7.96C16.7 7.82 16.59 7.7 16.48 7.58C16.37 7.47 16.24 7.37 16.11 7.28C15.98 7.19 15.83 7.11 15.69 7.05C15.54 6.98 15.38 6.94 15.23 6.91C15.07 6.87 14.91 6.86 14.75 6.86L9.3 6.86Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path></g></svg>
											</div>
											<!-- 重写 -->
											<div @click="rewrite(item)" v-if="item.type=='send'" title="编辑" class="buttons">
												<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.2286 17.3545H1.77142C1.34538 17.3545 1 17.6999 1 18.1259C1 18.552 1.34538 18.8973 1.77142 18.8973H18.2286C18.6546 18.8973 19 18.552 19 18.1259C19 17.6999 18.6546 17.3545 18.2286 17.3545Z" fill="currentColor"></path><mask id="mask0_400_418" maskUnits="userSpaceOnUse" x="1" y="1" width="15" height="15"><path d="M15.1429 1.10254H1V15.2454H15.1429V1.10254Z" fill="white"></path></mask><g mask="url(#mask0_400_418)"><path d="M2.48999 15.2425C2.36999 15.2425 2.26002 15.2225 2.15002 15.2025C2.04002 15.1725 1.94003 15.1325 1.84003 15.0825C1.73003 15.0325 1.63999 14.9825 1.54999 14.9025C1.45999 14.8325 1.39001 14.7525 1.32001 14.6625C1.25001 14.5825 1.19001 14.4825 1.14001 14.3825C1.09001 14.2825 1.05003 14.1725 1.03003 14.0625C1.01003 13.9525 1 13.8425 1 13.7225C1 13.6125 1.00998 13.5025 1.03998 13.3925L1.75 10.4325C1.9 9.81254 2.20001 9.28253 2.64001 8.83253L9.40002 2.08253C9.55002 1.92253 9.71997 1.78254 9.90997 1.66254C10.09 1.54254 10.28 1.44254 10.49 1.35254C10.69 1.27254 10.9 1.20254 11.12 1.16254C11.33 1.12254 11.55 1.10254 11.77 1.10254C12 1.10254 12.21 1.12254 12.43 1.16254C12.65 1.20254 12.86 1.27254 13.06 1.35254C13.27 1.44254 13.46 1.54254 13.64 1.66254C13.83 1.78254 14 1.92253 14.15 2.08253C14.31 2.24253 14.45 2.41254 14.57 2.59254C14.69 2.77254 14.79 2.97255 14.88 3.17255C14.96 3.37255 15.03 3.59254 15.07 3.80254C15.11 4.02254 15.13 4.24254 15.13 4.46254C15.13 4.68254 15.11 4.90253 15.07 5.11253C15.03 5.33253 14.96 5.54254 14.88 5.74254C14.79 5.95254 14.69 6.14254 14.57 6.32254C14.45 6.51254 14.31 6.68253 14.15 6.83253L7.40002 13.5925C6.95002 14.0425 6.42 14.3325 5.81 14.4825L2.84003 15.1925C2.73003 15.2225 2.60999 15.2425 2.48999 15.2425ZM11.67 2.73254C11.22 2.76254 10.84 2.94254 10.52 3.26254L3.78998 9.99254C3.55998 10.2225 3.41002 10.4925 3.33002 10.8125L2.66998 13.5625L5.42999 12.9025C5.73999 12.8225 6.02 12.6725 6.25 12.4425L13 5.68254C13.08 5.60254 13.15 5.52255 13.22 5.42255C13.28 5.33255 13.33 5.23254 13.38 5.12254C13.42 5.02254 13.45 4.91254 13.47 4.80254C13.5 4.68254 13.51 4.57254 13.51 4.46254C13.51 4.34254 13.5 4.23254 13.47 4.12254C13.45 4.01254 13.42 3.90254 13.38 3.79254C13.33 3.69254 13.28 3.59254 13.22 3.49254C13.15 3.40254 13.08 3.31254 13 3.23254C12.82 3.06254 12.62 2.92254 12.39 2.84254C12.16 2.75254 11.91 2.71254 11.67 2.73254Z" fill="currentColor"></path></g></svg>
											</div>
											<!-- 重新生成 -->
											<div @click="regenerate(item)" v-else title="重新生成" class="buttons">
												<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clipPath id="clip1258_20811"><rect width="20.000000" height="20.000000" fill="white" fill-opacity="0"></rect></clipPath></defs><rect width="20.000000" height="20.000000" fill="#FFFFFF" fill-opacity="0"></rect><g clip-path="url(#clip1258_20811)"><path id="path" d="M17.01 7.63L13.98 7.62C13.88 7.62 13.79 7.6 13.7 7.56C13.62 7.52 13.54 7.47 13.47 7.4C13.4 7.33 13.35 7.25 13.32 7.16C13.28 7.07 13.26 6.98 13.26 6.88C13.26 6.79 13.28 6.69 13.32 6.6C13.35 6.51 13.4 6.43 13.47 6.36C13.54 6.3 13.62 6.24 13.7 6.21C13.79 6.17 13.88 6.15 13.98 6.15L15.57 6.16C15.67 6.16 15.76 6.14 15.85 6.1C15.94 6.06 16.01 6.01 16.08 5.94C16.15 5.87 16.2 5.79 16.23 5.7C16.27 5.61 16.29 5.52 16.29 5.42L16.3 3.89C16.3 3.79 16.32 3.7 16.36 3.61C16.39 3.52 16.44 3.44 16.51 3.37C16.58 3.3 16.66 3.25 16.74 3.21C16.83 3.17 16.92 3.16 17.02 3.16C17.11 3.16 17.2 3.17 17.29 3.21C17.38 3.25 17.46 3.3 17.52 3.37C17.59 3.44 17.64 3.52 17.68 3.61C17.71 3.7 17.73 3.79 17.73 3.89L17.72 6.9C17.72 7 17.71 7.09 17.67 7.18C17.63 7.27 17.58 7.34 17.52 7.41C17.45 7.48 17.37 7.53 17.29 7.57C17.2 7.61 17.11 7.63 17.01 7.63Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M2.31 16.29L2.32 13.3C2.32 13.21 2.34 13.11 2.37 13.02C2.41 12.93 2.46 12.85 2.53 12.78C2.6 12.71 2.67 12.66 2.76 12.62C2.85 12.58 2.94 12.56 3.03 12.56L6.07 12.57C6.16 12.57 6.25 12.59 6.34 12.63C6.43 12.67 6.51 12.72 6.57 12.79C6.64 12.86 6.69 12.94 6.73 13.03C6.76 13.12 6.78 13.22 6.78 13.32C6.78 13.41 6.76 13.51 6.73 13.6C6.69 13.69 6.64 13.77 6.57 13.84C6.51 13.91 6.43 13.96 6.34 14C6.25 14.04 6.16 14.06 6.07 14.06L4.47 14.05C4.38 14.05 4.29 14.07 4.2 14.11C4.11 14.15 4.03 14.2 3.97 14.27C3.9 14.34 3.85 14.42 3.81 14.51C3.78 14.6 3.76 14.7 3.76 14.8L3.75 16.29C3.75 16.39 3.73 16.48 3.69 16.58C3.65 16.67 3.6 16.75 3.54 16.82C3.47 16.89 3.39 16.94 3.3 16.98C3.22 17.01 3.13 17.03 3.03 17.03C2.94 17.03 2.85 17.02 2.76 16.98C2.67 16.94 2.59 16.89 2.52 16.82C2.46 16.75 2.4 16.67 2.37 16.58C2.33 16.49 2.31 16.39 2.31 16.29Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M9.88 18.01C9.51 18.01 9.15 17.99 8.79 17.94C8.42 17.89 8.07 17.82 7.71 17.73C7.36 17.63 7.02 17.51 6.68 17.37C6.34 17.23 6.02 17.07 5.7 16.89C5.39 16.7 5.09 16.5 4.8 16.28C4.52 16.05 4.25 15.81 3.99 15.55C3.74 15.29 3.5 15.02 3.29 14.73C3.07 14.44 2.88 14.13 2.7 13.82L4.15 13.05C4.32 13.35 4.51 13.64 4.72 13.91C4.93 14.18 5.17 14.43 5.42 14.66C5.67 14.9 5.94 15.11 6.23 15.3C6.52 15.49 6.83 15.66 7.14 15.81C7.46 15.95 7.78 16.07 8.12 16.16C8.45 16.25 8.8 16.32 9.14 16.36C9.49 16.39 9.83 16.4 10.18 16.39C10.53 16.37 10.87 16.33 11.22 16.26C11.56 16.19 11.89 16.09 12.21 15.97C12.54 15.84 12.85 15.7 13.15 15.53C13.45 15.35 13.74 15.16 14.01 14.94C14.28 14.72 14.53 14.49 14.76 14.23C14.99 13.97 15.2 13.7 15.38 13.41C15.57 13.12 15.73 12.82 15.87 12.5C16 12.19 16.11 11.87 16.2 11.53C16.28 11.2 16.34 10.87 16.36 10.52C16.37 10.42 16.4 10.33 16.44 10.24C16.48 10.15 16.54 10.07 16.61 10C16.69 9.93 16.77 9.87 16.86 9.84C16.96 9.8 17.05 9.77 17.16 9.77C17.27 9.77 17.38 9.79 17.49 9.83C17.6 9.87 17.7 9.94 17.78 10.02C17.86 10.1 17.92 10.2 17.96 10.3C18 10.41 18.01 10.52 18 10.64C17.98 10.89 17.95 11.13 17.91 11.38C17.86 11.62 17.81 11.87 17.74 12.11C17.68 12.35 17.6 12.58 17.51 12.82C17.42 13.05 17.32 13.28 17.21 13.5C17.1 13.73 16.98 13.95 16.85 14.16C16.71 14.37 16.57 14.58 16.42 14.78C16.27 14.98 16.11 15.17 15.94 15.36C15.77 15.54 15.59 15.72 15.41 15.89C15.22 16.06 15.03 16.22 14.83 16.37C14.63 16.52 14.42 16.66 14.2 16.79C13.99 16.93 13.77 17.05 13.54 17.16C13.31 17.27 13.08 17.37 12.85 17.46C12.61 17.55 12.37 17.63 12.13 17.7C11.88 17.77 11.64 17.83 11.39 17.87C11.14 17.92 10.89 17.96 10.63 17.98C10.38 18 10.13 18.01 9.88 18.01Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path><path id="path" d="M2.85 10.27C2.73 10.28 2.62 10.26 2.51 10.22C2.4 10.17 2.31 10.11 2.23 10.03C2.14 9.95 2.08 9.85 2.04 9.74C2 9.63 1.99 9.52 2 9.41C2.03 8.98 2.1 8.56 2.2 8.15C2.3 7.73 2.43 7.33 2.6 6.94C2.76 6.54 2.96 6.16 3.19 5.8C3.41 5.44 3.67 5.1 3.95 4.77C4.24 4.45 4.54 4.15 4.88 3.88C5.21 3.6 5.56 3.35 5.93 3.13C6.3 2.91 6.69 2.73 7.09 2.57C7.5 2.41 7.91 2.28 8.33 2.19C8.75 2.09 9.18 2.03 9.62 2.01C10.05 1.98 10.48 1.99 10.91 2.03C11.35 2.07 11.77 2.14 12.19 2.25C12.61 2.36 13.02 2.5 13.42 2.67C13.81 2.84 14.19 3.04 14.56 3.28C14.92 3.51 15.27 3.77 15.59 4.05C15.91 4.34 16.21 4.64 16.48 4.98C16.75 5.31 17 5.66 17.21 6.03L15.78 6.83C15.61 6.54 15.42 6.25 15.2 5.99C14.98 5.73 14.74 5.48 14.49 5.25C14.23 5.02 13.96 4.82 13.66 4.63C13.37 4.45 13.07 4.29 12.75 4.15C12.44 4.01 12.11 3.9 11.77 3.82C11.44 3.73 11.1 3.67 10.76 3.64C10.41 3.61 10.07 3.6 9.72 3.62C9.37 3.64 9.03 3.69 8.69 3.77C8.36 3.84 8.03 3.94 7.71 4.07C7.38 4.2 7.08 4.35 6.78 4.52C6.48 4.7 6.2 4.89 5.94 5.11C5.67 5.33 5.43 5.57 5.2 5.83C4.97 6.08 4.77 6.36 4.59 6.65C4.41 6.94 4.25 7.24 4.12 7.55C3.98 7.87 3.88 8.19 3.8 8.52C3.72 8.85 3.66 9.19 3.64 9.53C3.63 9.62 3.6 9.72 3.56 9.81C3.52 9.9 3.46 9.98 3.39 10.05C3.32 10.12 3.23 10.17 3.14 10.21C3.05 10.25 2.95 10.27 2.85 10.27Z" fill="currentColor" fill-opacity="1.000000" fill-rule="nonzero"></path></g></svg>
											</div>

										</div>
									</div>
								</div>
							</template>

						</div>
						<!-- 输入对话区域 -->
						<div class="session_input_box">
							<div class="session_input_arrow" @click="goButtomEnforce();" :style="`opacity: ${scrollBottom>100?1:0};pointer-events: ${scrollBottom>100?'auto':'none'}; `">
								<svg fill="none" viewBox="0 0 24 24" width="1em" height="1em" class="t-icon t-icon-arrow-down" style="font-size: 24px;"><path fill="currentColor" d="M11 4.5v11.59l-4.5-4.5L5.09 13 12 19.91 18.91 13l-1.41-1.41-4.5 4.5V4.5h-2z"></path></svg>
							</div>
							<div class="session_input">
								<div class="session_input_warp">
									<div class="ai_input_box">
										<textarea id="chat-input" v-model="sendValue" class="ai_input" placeholder="发送消息" rows="2"></textarea>
										<div class="ai_input_text" v-html="sendValue"></div>
									</div>
									<div class="ai_input_option_box">
										<!-- <div @click="isThink = !isThink;" class="ai_button" :class="isThink?'ai_button_active':''">
											<svg class="ai_button_icon" width="19" height="19" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.656 17.344c-1.016-1.015-1.15-2.75-.313-4.925.325-.825.73-1.617 1.205-2.365L3.582 10l-.033-.054c-.5-.799-.91-1.596-1.206-2.365-.836-2.175-.703-3.91.313-4.926.56-.56 1.364-.86 2.335-.86 1.425 0 3.168.636 4.957 1.756l.053.034.053-.034c1.79-1.12 3.532-1.757 4.957-1.757.972 0 1.776.3 2.335.86 1.014 1.015 1.148 2.752.312 4.926a13.892 13.892 0 0 1-1.206 2.365l-.034.054.034.053c.5.8.91 1.596 1.205 2.365.837 2.175.704 3.911-.311 4.926-.56.56-1.364.861-2.335.861-1.425 0-3.168-.637-4.957-1.757L10 16.415l-.053.033c-1.79 1.12-3.532 1.757-4.957 1.757-.972 0-1.776-.3-2.335-.86zm13.631-4.399c-.187-.488-.429-.988-.71-1.492l-.075-.132-.092.12a22.075 22.075 0 0 1-3.968 3.968l-.12.093.132.074c1.308.734 2.559 1.162 3.556 1.162.563 0 1.006-.138 1.298-.43.3-.3.436-.774.428-1.346-.008-.575-.159-1.264-.449-2.017zm-6.345 1.65l.058.042.058-.042a19.881 19.881 0 0 0 4.551-4.537l.043-.058-.043-.058a20.123 20.123 0 0 0-2.093-2.458 19.732 19.732 0 0 0-2.458-2.08L10 5.364l-.058.042A19.883 19.883 0 0 0 5.39 9.942L5.348 10l.042.059c.631.874 1.332 1.695 2.094 2.457a19.74 19.74 0 0 0 2.458 2.08zm6.366-10.902c-.293-.293-.736-.431-1.298-.431-.998 0-2.248.429-3.556 1.163l-.132.074.12.092a21.938 21.938 0 0 1 3.968 3.968l.092.12.074-.132c.282-.504.524-1.004.711-1.492.29-.753.442-1.442.45-2.017.007-.572-.129-1.045-.429-1.345zM3.712 7.055c.202.514.44 1.013.712 1.493l.074.13.092-.119a21.94 21.94 0 0 1 3.968-3.968l.12-.092-.132-.074C7.238 3.69 5.987 3.262 4.99 3.262c-.563 0-1.006.138-1.298.43-.3.301-.436.774-.428 1.346.007.575.159 1.264.448 2.017zm0 5.89c-.29.753-.44 1.442-.448 2.017-.008.572.127 1.045.428 1.345.293.293.736.431 1.298.431.997 0 2.247-.428 3.556-1.162l.131-.074-.12-.093a21.94 21.94 0 0 1-3.967-3.968l-.093-.12-.074.132a11.712 11.712 0 0 0-.71 1.492z" fill="currentColor" stroke="currentColor" stroke-width=".1"></path><path d="M10.706 11.704A1.843 1.843 0 0 1 8.155 10a1.845 1.845 0 1 1 2.551 1.704z" fill="currentColor" stroke="currentColor" stroke-width=".2"></path></svg>
											<span class="ai_button_text">深度思考 (R1)</span>
										</div>
										<div @click="isNetwork = !isNetwork;" class="ai_button" :class="isNetwork?'ai_button_active':''">
											<svg class="ai_button_icon" width="17" height="17" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="10" cy="10" r="9" stroke="currentColor" stroke-width="1.8"></circle><path d="M10 1c1.657 0 3 4.03 3 9s-1.343 9-3 9M10 19c-1.657 0-3-4.03-3-9s1.343-9 3-9M1 10h18" stroke="currentColor" stroke-width="1.8"></path></svg>
											<span class="ai_button_text">联网搜索</span>
										</div> -->
										<div class="ai_input_option_right">
											<!-- <div class="ai_button_file">
												<svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 14 20" fill="none"><path d="M7 20c-1.856-.002-3.635-.7-4.947-1.94C.74 16.819.003 15.137 0 13.383V4.828a4.536 4.536 0 0 1 .365-1.843 4.75 4.75 0 0 1 1.087-1.567A5.065 5.065 0 0 1 3.096.368a5.293 5.293 0 0 1 3.888 0c.616.244 1.174.6 1.643 1.05.469.45.839.982 1.088 1.567.25.586.373 1.212.364 1.843v8.555a2.837 2.837 0 0 1-.92 2.027A3.174 3.174 0 0 1 7 16.245c-.807 0-1.582-.3-2.158-.835a2.837 2.837 0 0 1-.92-2.027v-6.22a1.119 1.119 0 1 1 2.237 0v6.22a.777.777 0 0 0 .256.547.868.868 0 0 0 .585.224c.219 0 .429-.08.586-.224a.777.777 0 0 0 .256-.546V4.828A2.522 2.522 0 0 0 7.643 3.8a2.64 2.64 0 0 0-.604-.876 2.816 2.816 0 0 0-.915-.587 2.943 2.943 0 0 0-2.168 0 2.816 2.816 0 0 0-.916.587 2.64 2.64 0 0 0-.604.876 2.522 2.522 0 0 0-.198 1.028v8.555c0 1.194.501 2.339 1.394 3.183A4.906 4.906 0 0 0 7 17.885a4.906 4.906 0 0 0 3.367-1.319 4.382 4.382 0 0 0 1.395-3.183v-6.22a1.119 1.119 0 0 1 2.237 0v6.22c-.002 1.754-.74 3.436-2.052 4.677C10.635 19.3 8.856 19.998 7 20z" fill="currentColor"></path></svg>
											</div> -->
											<div @click="sendMessage()" class="ai_button_send" :style="`opacity:${(!sendValue.trim() && taskState == 0) || taskState == 1?'0.3':'1'};`">
												<template v-if="taskState == 0">
													<svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 16c-.595 0-1.077-.462-1.077-1.032V1.032C5.923.462 6.405 0 7 0s1.077.462 1.077 1.032v13.936C8.077 15.538 7.595 16 7 16z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M.315 7.44a1.002 1.002 0 0 1 0-1.46L6.238.302a1.11 1.11 0 0 1 1.523 0c.421.403.421 1.057 0 1.46L1.838 7.44a1.11 1.11 0 0 1-1.523 0z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M13.685 7.44a1.11 1.11 0 0 1-1.523 0L6.238 1.762a1.002 1.002 0 0 1 0-1.46 1.11 1.11 0 0 1 1.523 0l5.924 5.678c.42.403.42 1.056 0 1.46z" fill="currentColor"></path></svg>
												</template>
												<template v-if="taskState == 1">
													<van-loading :size="15"/>
												</template>
												<template v-if="taskState == 2">
													<div style="width: 12px;height:12px;background:#FFF;border-radius: 3px;"></div>
												</template>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="session_input_hint" :style="`margin-bottom:${safeBottom+6}px;`">内容由 AI 生成，请仔细甄别</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 左侧历史划窗 -->
		<van-popup v-model:show="showSessionHistory" class="maxPopup" position="left" :style="{ width: '285px', height: '100%' }">
			<div class="history_box">
				<div class="history_header">
					<div class="ai_header">
						<svg @click="showSessionHistory = !showSessionHistory;" width="20" height="20" viewBox="0 0 20 20" class="ai_header_icon" fill="none" xmlns="http://www.w3.org/2000/svg"> <path id="Vector" d="M15.8333 2.5H4.16667C3.24619 2.5 2.5 3.24619 2.5 4.16667V15.8333C2.5 16.7538 3.24619 17.5 4.16667 17.5H15.8333C16.7538 17.5 17.5 16.7538 17.5 15.8333V4.16667C17.5 3.24619 16.7538 2.5 15.8333 2.5Z" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path id="Vector_2" d="M2.5 7.5H17.5" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path id="Vector_3" d="M7.5 17.5V7.5" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> </svg>
						<div class="ai_header_text" style="text-align:left;">历史对话</div>
					</div>
					<div class="chat_new_box">
						<div @click="newTask()" class="chat_new_warp">
							<svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path id="Vector" d="M1.5 3.66666C1.5 2.56209 2.39543 1.66666 3.5 1.66666H16.5C17.6046 1.66666 18.5 2.56209 18.5 3.66666V13C18.5 14.1046 17.6046 15 16.5 15H14.3333V18.3333L10 15H5.98883C5.22551 15 4.46921 14.8543 3.76048 14.5709L2.75722 14.1696C1.9979 13.8658 1.5 13.1304 1.5 12.3126V3.66666Z" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path id="Vector_2" d="M10 5V11" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path id="Vector_3" d="M7 8H13" stroke="#333333" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <defs> <clipPath id="clip0_13_513"> <rect width="20" height="20" fill="white"/> </clipPath> </defs> </svg>
							<span>新建对话</span>
						</div>
					</div>
				</div>
				<div class="history_body">
					<template v-for="(item,index) in historyData">
						<div v-if="item.time" class="history_time">{{item.time}}</div>
						<div class="history_item_warp" :class="chatId == item.id?'active':''">
							<div @click="switchSession(item)" class="history_item">{{item.title}}</div>
							<template v-if="chatId == item.id">
								<van-popover v-model:show="historyPopover" :placement="index>5&&index>historyData.length-5?'top-end':'bottom-end'" :actions="historyActions" @select="historySelect">
									<template #reference>
										<svg width="17" height="17" class="icon" style="padding: 3px 13px 3px 10px;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6148"><path d="M512 138.688m-74.688 0a74.688 74.688 0 1 0 149.376 0 74.688 74.688 0 1 0-149.376 0Z" fill="#6C6C6C" p-id="6149"></path><path d="M512 512m-74.688 0a74.688 74.688 0 1 0 149.376 0 74.688 74.688 0 1 0-149.376 0Z" fill="#6C6C6C" p-id="6150"></path><path d="M512 885.376m-74.688 0a74.688 74.688 0 1 0 149.376 0 74.688 74.688 0 1 0-149.376 0Z" fill="#6C6C6C" p-id="6151"></path></svg>
									</template>
								</van-popover>
							</template>
							
						</div>
					</template>
				</div>
			</div>
		</van-popup>

		<van-popup v-model:show="showRecordDetails" class="maxPopup" closeable position="bottom" :style="{ width: '100%', height: '90%' }">
			<template v-if="showRecordDetails">
				<iframe :src="recordDetails" width="100%" height="100%" frameBorder="0"></iframe>
			</template>
		</van-popup>

		<van-dialog v-model:show="dialogs.show" class="dialogs" :title="dialogs.title" show-cancel-button :before-close="dialogs.callback">
			<van-field v-model="dialogs.value" :maxlength="dialogs.max" :show-word-limit="dialogs.max && dialogs.max>0" :placeholder="dialogs.placeholder" />
		</van-dialog>
	</div>
</body>
<script type="text/javascript" src="../script/<EMAIL>"></script>
<script type="text/javascript" src="../script/<EMAIL>"></script>
<script type="text/javascript" src="../script/axios.min.js"></script>
<script type="text/javascript" src="../script/url.js"></script>
<script type="text/javascript" src="../script/jquery-3.6.4.min.js"></script>
<script type="text/javascript" src="../script/markdown-it.min.js"></script>
<script type="text/javascript" src="../script/dayjs.js"></script>
<script type="text/javascript" src="./js/aiGeneral.js"></script>
<script type="text/javascript">
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var vm,pageParam={},isApp;
	var recordTrigger = "录音纪要",isRecord = false;
	const md = markdownit({
		html: true, // 允许 HTML 标签
		linkify: true // 自动转换 URL 为链接
	});
	var vmData = {
		safeBottom:0,
		scrollTop:0,
		scrollBottom:0,
		appUrl:appUrl,
		appTheme:datas.appTheme||"#3657C0",
		token:datas.token,
		areaId:datas.areaId,
		type:datas.type,//ma会议助手 ta提案助手 aa建议助手 mad会议详情解读
		postParam:{},
		businessCode:"",
		title:"",
		chatId:"",
		aiIcon:"./img/icon_IntelligentAssistant.gif",
		initInfo:{
			// title:"您好！我是会务小助手",
			// summary:"您可以问我某某会议相关的问题",
			// questions:["我的会议日程","我的住房安排","我的用餐安排"]
		},
		historyPopover:false,
		historyActions:[
			{ type:"editName", text: '编辑名称' },
			// { text: '置顶' },
			{ type:"del",text: '删除',color:"rgb(250, 81, 81)" },
		],
		historyData:[
			// {id:"1",title:"Android 使用国密 SM4 的库",time:"今天"},
			// {id:"2",title:"在纯 HTML 和原生 JavaScript 环境中使用 gm-crypto 的方法"},
			// {id:"3",title:"TypeScript中使用sm-crypto库进行SM4加解密"},
			// {id:"4",title:"Android 使用国密 SM4 的库"},
		],
		listData:[
			// {type:"send",text:"劳动法"},
			// {type:"receive",text:"<p>你好我是回答内容</p>",think:"已深度思考（用时 70 秒）",showThink:true,thinkText:"<p>我是思考内容</p>"},
			// {type:"association",text:"你还想问？ ➔"},
		],
		showSessionHistory:false,//打开历史面板
		sendValue:"",//搜索框文字
		isThink:true,//是否深度思考
		isNetwork:false,//是否联网搜索
		// isSend:false,//是否可以点击发送
		taskState:0,//任务状态 0无任务 1接口请求中 2返回文字中

		showRecordDetails:false,
		recordDetails:"",

		dialogs:{
			show:false,
			title:"",
			value:"",
			placeholder:"",
			callback:()=>{},
			max:0,
		}
	};
	var vmWatch = {
		showSessionHistory(_value){
			if(!_value){
				this.historyPopover = false;
			}
		},
		taskState(){
			console.log("taskState:"+this.taskState);
		}
	};
	var methods = {
		init(){
			var that = this;
			that.chatId = generateSessionID();//生成 唯一 chatid
			window.postType = 1;
			switch(that.type){
				case "maf"://会议文件提取
					window.postType = 0;
					that.businessCode = "meetFile";
					that.sendMessage("核心内容提取");
					break;
				case "ma"://会议智能助手
					that.businessCode = "meet_assistant";//
					that.initInfo = {
						title:"您好！我是会务小助手",
						summary:"您可以问我会议相关的问题",
						questions:["我的会议日程","我的酒店和房间号是多少","我的用餐安排"]
					};
					that.postParam = {
						attachmentIds:datas.attachmentIds,
					};
					break;
				case "maa"://会议录音纪要
					that.businessCode = "meet_summary_chat";//
					setTimeout(() => {
						that.sendMessage(recordTrigger);
					}, 200);
					break;
				case "maru"://会议任务成果上传
					that.businessCode = "meet_task_assistant";//
					that.postParam = {
						tool:"1_MEET",
						"param":{
							"description":datas.description,
							"achievement":datas.achievement
						}
					};
					that.initInfo = {
						title:"您好！我是会务小助手",
						summary:"我可以辅助您完成任务",
						questions:["如何高效完成任务？请给出具体执行方案。"]
					};
					if(datas.achievement){
						that.initInfo.questions.push("评审任务成果，并给出优化建议。");
					}
					break;
				case "mara"://会议任务成果审核
					that.businessCode = "meet_task_assistant";//
					that.postParam = {
						tool:"1_MEET",
						"param":{
							"description":datas.description,
							"achievement":datas.achievement
						}
					};
					that.initInfo = {
						title:"您好！我是会务小助手",
						summary:"我可以辅助您审核任务",
						questions:["审核任务成果，并给出是否通过审核的结论。"]
					};
					break;
			}
			that.getHistoryData();
			//自动滚动时可滑动区域
			function stop(){
				window.dotScroll = true;
				$(".session_items_box").stop();
				clearTimeout(window.dotTask);
				window.dotTask = setTimeout(() => {
					window.dotScroll = false;
				}, 2000);
			}
			$(".session_items_box").on("touchstart", stop);
			$(".session_items_box").on("touchmove", stop);
			$(".session_items_box").on("touchend", stop);
			$(".session_items_box").on("click", stop);
			$(".session_items_box").on("wheel", stop);
			// 使用 jQuery 监听滚动事件
			$("#assistant").on('scroll', function(event) {
				// 获取纵向滚动位置（像素）
				var scrollTop = $(this).scrollTop();
				var totalHeight = this.scrollHeight;
				var visibleHeight = $(this).height();
				that.scrollTop = scrollTop;
				that.scrollBottom = (totalHeight - 10) - (scrollTop + visibleHeight);
				if (that.scrollBottom <= 0) {
					window.dotScroll = false;
					// console.log("到底部了");
				}else{
					// console.log("距离底部还有：" + that.scrollBottom);
				}
			});
		},
		//取消任务
		cancelTask(){
			var that = this;
			if(window.controller){
				try{
					window.controller.abort();
				}catch(e){
					console.log(e);
				}
			}
		},
		//发送消息
		sendMessage(_msg,_item,_other,_callback){
			var that = this;
			var sendValue = _msg || that.sendValue.trim();
			console.log("是否正在录音："+isRecord + ":" + (zyTencentAsrRealtime?Object.keys(zyTencentAsrRealtime):"无插件"));
			if(!isRecord && sendValue == recordTrigger){//有插件，并且是录音纪要触发词  zyTencentAsrRealtime && 
				var udid = generateSessionID();
				that.listData.push({type:"send",udid:udid,text:sendValue});
				//recordState录音状态 1未开始 2录音中 3生成中 4完成  recordCache识别中的可变数据 recordData识别完的段落数据
				var receiveBody = {type:"receive",udid:udid,question:sendValue,recordState:1,dotOptions:true,title:(dayjs().format('M月D日'))+recordTrigger,recordTime:0,text:`请点击开始${recordTrigger}！`,btnText:"开始",recordData:[],recordCache:"",showText:"",think:"",showThink:true,thinkText:""};
				// var receiveBody = {type:"receive",udid:udid,question:sendValue,recordState:2,dotOptions:true,title:(dayjs().format('M月D日'))+recordTrigger,recordTime:10,text:`正在识别...`,btnText:"结束"+recordTrigger,recordData:[{text:"我有一个问题。"}],recordCache:"我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？我是在说什么呢？",showText:"",think:"",showThink:true,thinkText:""};
				// var receiveBody = {type:"receive",udid:udid,question:sendValue,recordState:3,dotOptions:true,title:(dayjs().format('M月D日'))+recordTrigger,recordTime:10,text:`正在理解音频...`,btnText:recordTrigger+"生成中...",recordData:[{text:"我有一个问题。"}],recordCache:"我是在说什么呢？",showText:"",think:"",showThink:true,thinkText:""};
				// var receiveBody = {type:"receive",udid:udid,question:sendValue,recordState:4,dotOptions:true,title:(dayjs().format('M月D日'))+recordTrigger,time:dayjs().valueOf(),recordTime:10,text:`XXXX的任务`,btnText:"查看详情",recordData:[{text:"我有一个问题。"}],recordCache:"我是在说什么呢？",showText:"",think:"",showThink:true,thinkText:""};
				that.listData.push(receiveBody);
				that.goButtomEnforce();
				that.sendValue = "";
				return;
			}
			if(that.taskState == 1 || (that.taskState == 0 && !sendValue)){//正在请求接口，或者没内容 没附件
				return;
			}else if(that.taskState == 2){//正在识别，取消
				that.taskState = 0;
				that.cancelTask();
				return;
			}
			that.taskState = 1;
			var receiveBody;
			if(!_item){
				var udid = generateSessionID();
				that.listData.push({type:"send",udid:udid,text:sendValue});
				receiveBody = {type:"receive",udid:udid,question:sendValue,text:"",showText:"",think:"",showThink:true,thinkText:""};
				that.listData.push(receiveBody);
			}else{
				receiveBody = _item;
			}
			that.goButtomEnforce();
			that.sendValue = "";
			window.isTask = false;
			window.showAssociation = [];
			that.cancelTask();
			window.controller = new AbortController();
			var postBody = JSON.stringify({
				...[
					{
						chatId:that.chatId,
						businessId:datas.id,
						businessCode:that.businessCode,
						stream:true,
						detail:true,
						"messages": [
							{
								"role": "user",
								"content": sendValue
							}
						]
					},{
						chatId:that.chatId,
						businessId:datas.id,
						chatBusinessScene:that.businessCode,
						question:sendValue,
					}
				][window.postType],
				...(_other||{}),
				...that.postParam
			})
			console.log(postBody);
			fetch(
				`${that.appUrl}${["meetai/completions","aigpt/chatStream"][window.postType]}`,
				{
					method: 'POST',
					headers: {
						'Accept': 'text/event-stream',
						'Content-Type': 'application/json',
						"u-login-areaId": that.areaId,
						"Authorization": that.token,
						"u-terminal":"APP",
					},
					body: postBody,
					signal: window.controller.signal,  // 传递 signal
				}
			).catch(function (err) {
				that.taskState = 0;
				console.log("abc:"+err.name);
				receiveBody.text = err.name == "TypeError"?"服务器繁忙，请稍后再试。":" ";
				that.goButtom();
			}).then(function (response) {
				if(!response)return;
				var reader = response.body.getReader();  // 获取流的读取器
				var decoder = new TextDecoder();  // 用于解码字节数据
				let buffer = '';
				that.taskState = 2;
				receiveBody.text = "";
				that.goButtom();
				// 逐步处理流数据
				reader.read().then(function processText({ done, value }) {
					if (done) {
						return;
					}
					buffer += decoder.decode(value, { stream: true });
					// 根据换行符分割事件数据
					var lines = buffer.split('\n\n');
					if(lines.length == 1 && lines[0].startsWith('{')){
						var data = JSON.parse(lines[0]);
						receiveBody.text += data.message || data.data;
						if(data.code != 200){
							that.taskState = 0;
						}
						that.$forceUpdate();
					}
					for (let i = 0; i < lines.length - 1; i++) {
						var line = lines[i];
						if (line.startsWith('data:')) {
							var eventData = line.substring(5).trim();
							try{
								eventData = JSON.parse(eventData);
							}catch(e){
								// console.log(e)
							}
							if(typeof(eventData) == "object"){
								var choice = eventData.choices || [{}];
								var details = choice[0].delta || {};
								if(eventData.role == "assistant"){
									var content = eventData.content;
									if(content == null){//结束
										that.taskState = 0;
										if(!receiveBody.text)receiveBody.text = "无数据返回";
										if(!that.title){
											setTimeout(() => {
												that.getHistoryData();
											}, 500);
										}
										setTimeout(() => {
											that.getAddData();
										}, 500);
										_callback && _callback();
									}else{
										receiveBody.text += eventData.content || "";
										try{
											receiveBody.showText = dealMark(receiveBody.text);
										}catch(e){
											receiveBody.showText = receiveBody.text;
										}
										that.$forceUpdate();
									}
								}else if(details){
									if (details.hasOwnProperty('reasoning_content')) {//深度

									}
									if (details.hasOwnProperty('content')){
										receiveBody.text += details.content || "";
										try{
											receiveBody.showText = dealMark(receiveBody.text);
										}catch(e){
											receiveBody.showText = receiveBody.text;
										}
										that.$forceUpdate();
									}
								}
							}else if(eventData == "[DONE]"){//返回结束
								that.taskState = 0;
								if(!receiveBody.text)receiveBody.text = "无数据返回";
								if(!that.title){
									setTimeout(() => {
										that.getHistoryData();
									}, 500);
								}
								setTimeout(() => {
									that.getAddData();
								}, 500);
								_callback && _callback();
							}
							that.goButtom(receiveBody.recordState?`#${receiveBody.type}_${receiveBody.udid} .session_items_record_content`:'');
						}
					}
					buffer = lines[lines.length - 1];
					reader.read().then(processText);
				});
			});
		},

		//获取更多对话
		getAddData(){
			var that = this;
			that.ajax(`${that.appUrl}meetai/createQuestionGuide`,
			{
				chatBusinessScene:that.businessCode,
				chatId:that.chatId,
			},(ret,err)=>{
				if(ret && ret.code == 200){
					var data = ret.data || [];
					data.forEach((_item,_index) => {
						that.listData.push({type:"association",oldText:_item,text:_item+" ➔"});
					});
					that.goButtom();
					that.$forceUpdate();
				}
			});
		},
		//获取历史对话
		getHistoryData(){
			var that = this;
			that.ajax(`${that.appUrl}aigptChatCluster/list`,
			{
				pageNo:1,
				pageSize:99,
				query:{
					chatBusinessScene:that.businessCode,
				}
			},(ret,err)=>{
				if(ret && ret.code == 200){
					var data = ret.data || [];
					var newList = [];
					data.forEach((_item,_index) => {
						if(_item.id == that.chatId){
							that.title = _item.userQuestion;
						}
						var showTime = getTimeCategory(_item.createDate);
						nowTime = showTime;
						if(_index && nowTime == newList[_index-1].createDate){
							nowTime = "";
						}
						newList.push({id:_item.id,businessCode:_item.chatBusinessScene,title:_item.userQuestion,time:nowTime,createDate:showTime});
					});
					that.historyData = newList;
				}
			});
		},
		goButtomEnforce(_el){
			this.scrollBottom = 0;
			window.dotScroll = false;
			this.goButtom(_el);
		},
		goButtom(_el){
			var that = this;
			try{
				if($(_el || "#assistant") && !window.dotScroll){
					$(_el || "#assistant").stop();
					$(_el || "#assistant").animate({scrollTop:$(_el || "#assistant").prop("scrollHeight")},200);
				}
			}catch(e){
			}
		},
		//新对话
		newTask(_type){
			var that = this;
			if(isRecord){
				toast(`正在${recordTrigger}，请先结束`);
				return;
			}
			if(!_type && !that.listData.length){
				toast("已经在最新的对话中");
				return;
			}
			that.chatId = generateSessionID();//生成 唯一 chatid
			that.listData = [];
			that.taskState = 0;
			that.title = "";
			that.cancelTask();
			that.showSessionHistory = false;
			if(that.type == "maa"){
				setTimeout(() => {
					that.sendMessage(recordTrigger);
				}, 200);
			}
		},
		//切换旧对话
		switchSession(_item){
			var that = this;
			that.chatId = _item.id;
			that.businessCode = _item.businessCode;
			that.title = _item.title;
			that.cancelTask();
			that.showSessionHistory = false;
			//切换会话后，获取历史数据
			that.ajax(`${that.appUrl}aigptChatLogs/list`,
			{
				pageNo:1,
				pageSize:50,
				query:{
					chatId:that.chatId,
				}
			},(ret,err)=>{
				if(ret && ret.code == 200){
					var data = ret.data || [];
					var newList = [];
					data.forEach((_item,_index) => {
						var sendValue = _item.userQuestion;
						var answer = _item.answer || "无数据返回";
						var createDate = _item.createDate;
						newList.push({type:"send",udid:_item.id,text:sendValue});
						var voiceText = _item.voiceText || "";
						var showText;
						try{
							showText = dealMark(answer);
						}catch(e){
							showText = answer;
						}
						var receiveBody = {type:"receive",udid:_item.id,question:sendValue,text:answer,showText:showText,time:createDate,think:"",showThink:true,thinkText:""};
						if(voiceText){
							var voices = voiceText.split('|zy|');
							receiveBody = {type:"receive",udid:_item.id,question:sendValue,recordState:4,dotOptions:true,title:voices[0],time:dayjs(createDate).format('MM/DD'),recordTime:Number(voices[1]),text:answer,showText:showText,btnText:"查看详情",recordData:[],recordCache:"",think:"",showThink:true,thinkText:""};
						}
						newList.push(receiveBody);
					});
					that.listData = newList;
					setTimeout(() => {
						that.goButtomEnforce();
						that.getAddData();
					}, 100);
				}
			});

		},
		//历史列表中点击更多回调
		historySelect(action){
			var that = this;
			switch(action.type){
				case "editName":
					that.dialogs = {
						show:true,
						title:action.text,
						value:that.title,
						placeholder:"请输入名称",
						max:20,
						callback:(action)=>{
							return new Promise((resolve) => {
								if(action !== 'confirm' || that.dialogs.value == that.title){
									resolve(true);
									return;
								}
								that.ajax(`${that.appUrl}aigptChatCluster/edit`,
								{
									form:{
										id:that.chatId,
										userQuestion:that.dialogs.value
									}
								},(ret,err)=>{
									if(ret && ret.code == 200){
										that.getHistoryData();
										resolve(true);
									}else{
										toast("编辑失败");
										resolve(false);
									}
								});
							});
						}
					};
					break;
				case "del":
					that.ajax(`${that.appUrl}aigptChatCluster/dels`,
					{
						ids:[that.chatId]
					},(ret,err)=>{
						if(ret && ret.code == 200){
							that.newTask(1);
							that.getHistoryData();
						}else{
							toast("删除失败");
						}
					});
					break;
			}
		},
		//重写
		rewrite(_item){
			var that = this;
			if(that.sendValue.trim()){
				alert({msg:"输入框已有内容,是否覆盖?"},(ret)=>{
					if(ret.buttonIndex == 1){
						that.sendValue = that.convertTextarea(_item.text);
					}
				});
				return;
			}
			that.sendValue = that.convertTextarea(_item.text);
		},
		//重新生成
		regenerate(_item){
			var that = this;
			that.listData = that.listData.filter(item => item.udid !== _item.udid);
			that.sendMessage(_item.question);
		},
		//点击提示语
		openAssociation(_item){
			window.dotScroll = false;
			this.sendMessage(_item.oldText);
		},
		//点击录音记要按钮
		recordBtn(_item){
			var that = this;
			//recordState录音状态 1未开始 2录音中 3生成中 4完成 5生成失败继续
			switch(_item.recordState){
				case 1:
					if(isRecord){
						toast(`正在${recordTrigger}，请先结束`);
						return;
					}
					that.initRecordModule(_item);
					break;
				case 2:
					alert({title:`结束${recordTrigger}吗？`,msg:"目前正在录音，确定要结束吗"},(ret)=>{
						if(ret.buttonIndex == 1){
							zyTencentAsrRealtime.stop();
						}
					});
					break;
				case 4:
					// window.location = `./aiRecordDetails.html?id=${_item.udid}&areaId=${that.areaId||""}&token=${that.token||""}&appTheme=${that.appTheme||""}`;
					that.recordDetails = `./aiRecordDetails.html?id=${_item.udid}&areaId=${that.areaId||""}&token=${that.token||""}&appTheme=${that.appTheme||""}`;
					that.showRecordDetails = true;
					break;
				case 5:
					that.upDataRecord(_item);
					break;
			}
		},
		//初始化录音插件
		initRecordModule(_item){
			var that = this;
			if(!isApp){
				toast("该功能本端暂不支持，尽请期待，请使用APP");
				return;
			}
			if(!window.asrKeys){
				toast("未维护asrkey，请联系管理员");
				return;
			}
			var granted = api.hasPermission({ list:['microphone'] })[0].granted;
			if(!granted){
				api.requestPermission({ list:['microphone'] }, function(ret, err){
					if (ret.list[0].granted) {
						that.initRecordModule(_item);
					}
				});
				return;
			}
			var keys = window.asrKeys.split(',');
			zyTencentAsrRealtime.init({
				appId:keys[0],
				secretId:keys[1],
				secretKey:keys[2],
			});
			window.asrError = false;
			zyTencentAsrRealtime.addEventListener(function(ret,err){
				if(window.asrError)return;
				if(ret.status == "recognizeState"){//录音回调
					if(ret.eventType == "startRecord"){//开始录音
						console.log(JSON.stringify(ret));
						isRecord = true;
						_item.recordState = 2;
						_item.text = "正在识别...";
						_item.btnText = "结束"+recordTrigger;
						that.recordTimeAdd(_item);
					}else if(ret.eventType == "stopRecord"){//结束录音
						console.log(JSON.stringify(ret));
						isRecord = false;
						_item.filePath = ret.filePath;
						that.upDataRecord(_item);
					}else if(ret.eventType == "voiceDb"){//音量变化
						//console.log(JSON.stringify(ret));
					}
				}else if(ret.status == "recognizeResult"){//实时识别回调
					if(ret.eventType == "sliceSuccess"){//返回分片的识别结果，此为中间态结果，会被持续修正
						console.log(JSON.stringify(ret));
						_item.recordCache = ret.text;
						that.goButtom(`#${_item.type}_${_item.udid} .session_items_record_content`);
					}else if(ret.eventType == "segmentSuccess"){//返回语音流的识别结果，此为稳定态结果，可做为识别结果用与业务
						console.log(JSON.stringify(ret));
						if(ret.text.trim()){
							_item.recordData.push({text:ret.text,startTime:ret.startTime,endTime:ret.endTime});
						}
						_item.recordCache = "";
						that.goButtom(`#${_item.type}_${_item.udid} .session_items_record_content`);
					}else if(ret.eventType == "success"){//识别结束回调，返回所有的识别结果
						console.log(JSON.stringify(ret));
					}else if(ret.eventType == "failure"){//识别失败
						window.asrError = true;
						console.log(JSON.stringify(ret));
						isRecord = false;
						_item.recordState = 1;
						_item.text = "识别失败,"+(ret.response || ret.clientException || ret.serverException || "");
						_item.btnText = "重新开始";
					}
				}
			});
			zyTencentAsrRealtime.start();
		},
		//自增时间
		recordTimeAdd(_item){
			var that = this;
			setTimeout(() => {
				if(_item.recordState == 2){
					_item.recordTime++;
					that.recordTimeAdd(_item);
				}
			}, 1000);
		},
		//上传录音
		upDataRecord(_item){
			var that = this;
			_item.recordState = 3;
			_item.text = "正在理解音频...";
			_item.btnText = recordTrigger+"生成中...";
			console.log(JSON.stringify(_item));
			that.ajax(`${that.appUrl}aigpt/save/voiceText`,
			{
				voiceText:JSON.stringify(_item.recordData.map(obj=>{return {time:obj.startTime,startTime:obj.startTime,endTime:obj.endTime,text:obj.text}}))
			},(ret,err)=>{
				console.log(JSON.stringify(ret));
				if(ret && ret.code == 200){
					that.sendMessage("总结会议纪要",_item,{
						attachmentIds:ret.data.id,
						logId:_item.udid,
						voiceText:_item.title + "|zy|" + _item.recordTime + "|zy|" + JSON.stringify(_item.recordData)
					},()=>{
						_item.recordState = 4;
						_item.btnText = "查看详情";
						setTimeout(() => {
							if(_item.voiceIds){
								that.ajax(`${that.appUrl}aigptChatLogs/edit`,{
									form:{
										id:_item.udid,
										voiceIds:_item.voiceIds
									}
								},(ret,err)=>{
									console.log("保存原文件id："+JSON.stringify(ret));
								})
							}
						}, 300);
					});
					//上传录音原文件
					if(_item.filePath){
						that.ajax(`${that.appUrl}file/upload`,{
							files: { file: _item.filePath }
						},(ret,err)=>{
							console.log("上传原文件："+JSON.stringify(ret));
							_item.voiceIds = ret.data.id;
						})
					}
				}else{
					_item.recordState = 5;
					_item.btnText = "重新生成";
					_item.text = "识别失败,"+JSON.stringify(ret ? ret.message || ret.msg || ret.body || ret.data : err.msg || err.body || "");
				}
			});
		},
	};

	//历史时间展示
	function getTimeCategory(timestamp) {
		// 获取当前日期0点时间戳
		var now = new Date();
		var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
		var todayTime = today.getTime();
		
		// 获取目标日期0点时间戳
		var targetDate = new Date(timestamp);
		var targetDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());
		var targetDayTime = targetDay.getTime();
		
		// 计算天数差（精确到自然日）
		var diffDays = Math.floor((todayTime - targetDayTime) / (24 * 60 * 60 * 1000));
		
		// 分类判断
		if (diffDays === 0) {
			return '今天';
		} else if (diffDays === 1) {
			return '昨天';
		} else if (diffDays <= 6) {
			return '7天';
		} else if (diffDays <= 29) {
			return '30天';
		} else {
			return '更早';
		}
	}
	
</script>
</html>