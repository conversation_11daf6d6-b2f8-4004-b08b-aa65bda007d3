<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>app下载</title>
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0; }
		.down_topimg{ width: 100%; height: auto; }
		.down_body{ padding:50px 15px 0;text-align: center; }
		.down_icon{ width: 90px; height: 90px; }
		.down_appName{ color: #000; font-size: 24px; font-weight: 600; margin-top: 20px; margin-bottom: 30px; }
		.down_hint{ padding-top: 10px; color: #6C7D89; font-size: 16px; }
		.down_qr{ width: 120px; height: 120px; margin-top: 20px; margin-bottom: 27px; cursor: pointer; transition: all 0.25s; }
		.down_qr:hover { transform: scale(1.1); box-shadow: 0 0 10px rgba(21, 21, 21, 0.2); border: none; }
		.down_btn_box{  }
		.down_btn{ width: 150px; padding: 8px 0; background-color: #28caad; font-size: 16px; border: 1px solid transparent; font-weight: 600!important; display: inline-block; color: #FFF; border-radius: 35px; margin: 5px 7px; cursor: pointer; }
	</style>
</head>
<body>
	<div v-cloak id="app">
		<img class="down_topimg" src="./images/top_bg_middle.png" alt=""/>
		<div class="down_body">
			<img class="down_icon" :src="icon" alt=""/>
			<div class="down_appName">{{appName}}</div>
			<div class="down_hint">用手机扫描二维码安装</div>
			<img class="down_qr" :src="qr" alt=""/>
			<div class="down_btn_box">
				<button v-if="sysType=='ios'||sysType=='pc'" @click="downLoad(1)" class="down_btn">下载 iOS 版</button>
				<button v-if="sysType=='android'||sysType=='pc'" @click="downLoad(2)" class="down_btn">下载 Android 版</button>
			</div>
			<!-- <div class="down_hint">本应用其它说明</div> -->
		</div>
	</div>
</body>
<script type="text/javascript" src="./script/vue.min.js"></script>
<script type="text/javascript" src="./script/axios.min.js"></script>
<script type="text/javascript" src="./script/url.js"></script>
<script type="text/javascript">
	var vm,pageParam={};
	var vmData = {
		icon:"",
		appName:"",
		apkUrl:"",
		qr:"",
		appUrl:appUrl,
		downErr:"请稍候",//异常提示
		sysType:"",
	};
	var methods = {
		init:function(){
			var that = this;
			var userAgent = navigator.userAgent.toLowerCase();
			if (userAgent.indexOf('android') !== -1) { 
				that.sysType = "android";
			} else if (userAgent.indexOf('iphone') !== -1 || userAgent.indexOf('ipad') !== -1 || userAgent.indexOf('ipod') !== -1) {
				that.sysType = "ios";
			}else{
				that.sysType = "pc";
			}
			axios.get(`${that.appUrl}zyApkFile/findLastVersion`).then(function (ret) {
				ret = ret.data || {};
				dealAjaxContent(ret);
				if(ret.code == 200 && ret.data && ret.data.length){
					that.downErr = "";
					var data = ret.data[0];
					that.icon = `${that.appUrl}image/${data.icon}`;
					that.appName = data.fileName;
					that.apkUrl = data.filePath.indexOf('http')==0?data.filePath:`${that.appUrl}file/preview/${data.filePath}`;
					that.qr = `${location.protocol.indexOf("http")!=-1?location.protocol:'http:'}//cszysoft.com:909${location.protocol.indexOf("http")!=-1&&location.protocol=='https:'?'1':'0'}/utils/qr?text=${location.href}&logo=${that.icon}`;
				}else{
					that.downErr = ret.message || ret.data;
					alert(that.downErr);
				}
			}).catch(function (err) {
				that.downErr = err;
				alert(that.downErr);
			});
		},
		downLoad:function(_type){
			var that = this;
			if(that.downErr){
				alert(that.downErr);
				return;
			}
			if(_type == 1){
				axios.get(`${that.appUrl}zyAppDownloadUrl/findDownLoadUrl`).then(function (ret) {
					ret = ret.data || {};
					dealAjaxContent(ret);
					if(ret.code == 200 && ret.data){
						window.location.href = (ret.data);
					}else{
						alert(ret.message || ret.data);
					}
				}).catch(function (err) {
					alert(err);
				});
			}else{
				var userAgent = navigator.userAgent.toLowerCase();
				if(userAgent.indexOf('micromessenger') !== -1){
					alert("请点击右上【···】在浏览器打开下载");
					return;
				}
				window.location.href = (that.apkUrl);
			}
		},
	};

	window.onload = function() {
		showVue();
	};
	
	function showVue(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
</script>
</html>