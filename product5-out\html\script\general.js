/**
 * 介绍：通用页面引用js 每个页面必须增加 
 * var vmData,vmWatch,vmMethods;
 */
//当前层级
var scripts = $api.domAll("script"), scriptPrefix = "";
for (var i = 0; i < scripts.length; i++) {
	var scriptsSrc = $api.attr(scripts[i], "src");
	if (scriptsSrc && scriptsSrc.indexOf('http') != 0) {
		scriptPrefix = scriptsSrc.split('script')[0];
	}
}
//loadjscssfile(scriptPrefix + "css/icon/iconfont.css","css");//引入字体文件
//动态引用文件
function loadjscssfile(filename,filetype){
	var fileref = null;
	if(filetype == "js"){
		fileref = document.createElement('script');
		fileref.setAttribute("type","text/javascript");
		fileref.setAttribute("src",filename);
	}else if(filetype == "css"){
		fileref = document.createElement('link');
		fileref.setAttribute("rel","stylesheet");
		fileref.setAttribute("type","text/css");
		fileref.setAttribute("href",filename);
	}
	if(typeof fileref != "undefined"){
		document.getElementsByTagName('head')[0].appendChild(fileref);
	}
}
//Vue对象
var vm,general_fs,general_docReader;
var userInfo = "";
/**
 * 默认data参数 每个页面都有的参数
 */
var DEFAULT_DATA = {
	scrollTop:0,//页面划动距离
	safeAreaTop:0,//手机端状态栏高度
	safeAreaBottom:0,//iphoneX 等有底部虚拟条的
	pageParam:{},//接收前一个页面的参数
	pageType:"",//页面类型 page或home 默认为page
	refreshPageSize:0,//返回当前页刷新列表的条数
	
	showSkeleton:true,//是否展示骨架屏
	footerBtnsShow:true,//右下角按钮是否隐藏
	
	appFont: T.getPrefs("appFont") || "微软雅黑",//app全局字体
	appFontSize:Number(T.getPrefs("appFontSize") || 16),//app全局字体大小
	appTheme:T.getPrefs("appTheme") || "#007BFF",//app全局主题色
	
	//网页版新增	 是否刷新中	刷新是否禁用	刷新成功提示
	appRefreshisDisabled:false,//用户选择是否禁用
	appRefresh:{isLoading:false,isDisabled:false,successText:"刷新成功"},
	//列表相关属性	是否处于加载状态	 是否已加载完成	是否已加载完成	是否加载失败，加载失败后点击错误提示可以重新触发load事件
	appLoad:{isLoading:false,isFinished:false,finishedText:"",isError:false,errorText:T.NET_ERR,offset:100},
	
	//列表相关	区分下拉还是上拉	为1页时是下拉刷新
	pageNo:1,//当前页码
	pageSize:10,//当前请求条数
	
	seachText:"",//搜索词
	seachPlaceholder:"请输入搜索内容",//搜索提示
	
	firstAjax:false,//首次网络请求是否成功
	dotCloseListener:false,//当前页面不要划动返回
	
	pageNot:{type:0,url:"",text:"",summary:"",hasBtn:true,data:[
		{url:scriptPrefix+"images/empty4.png",text:"暂无数据",summary:"去看看别的吧~"},
		{url:scriptPrefix+"images/empty1.png",text:"网络不小心断开了",summary:"请刷新试试看吧~"},
		{url:scriptPrefix+"images/empty0.png",text:"页面不小心丢失啦",summary:"请刷新试试看吧~"},
		{url:scriptPrefix+"images/empty3.png",text:"暂无权限",summary:"您还没有权限查看"},
		{url:scriptPrefix+"images/empty2.png",text:"图片加载失败",summary:"请刷新试试看吧~"},
		{url:scriptPrefix+"images/empty5.png",text:"暂无消息",summary:"您还没有收到消息哦~"},
	]},//页面空时的提示词
	
	iszx:false,//是人大还是政协项目

	pathname:"",//当前页面名
	isApp:false,//当前是否app环境
};
/**
 * 默认watch参数 每个页面都有 
 */
var DEFAULT_WATCH = {
	"refreshPageSize":function(_val){
		window.sessionStorage.setItem(vm.pathname+"_refreshPageSize_"+(vm.pageParam.recordId||""),_val||"");
	},
	"seachText":function(_val){//搜索文字
		window.sessionStorage.setItem(vm.pathname+"_seachText_"+(vm.pageParam.recordId||""),_val || "");
	},
};
/**
 * 默认methods参数 每个页面都有 
 */
//http://localhost:8080/s/app_genaral/index.html?ndata=userId-zy-1-@-recordId-zy-348292424029700096-@-relateType-zy-36-@-sysUrl-zy-http://test.dc.cszysoft.com:21408/lzt/-@-only-zy-1624524559
var DEFAULT_METHODS = {
	//默认的初始化方法
	defalutInit:function(){
		var that = this;
		that.initPageParam();
		that.changeConfiguration();
		that.cleanIosDelay();
		window.webLoad = true;
		try{
			initApp();
		}catch(e){
		}
		document.title = that.pageParam.title || "";
		that.fitWidth();
		if((that.pageParam.closewx || "true") == "true"){
			$api.removeCls($api.byId("app"), "none");
			setTimeout(function(){
				try{that.init();}catch(e){console.error(e.message)}
			},1);
			return;
		}
		userInfo = T.getPrefs('userInfo');
		if(that.pageParam.testUser == "true" && !userInfo){
			userInfo = {"openid":"oemup55oyPn_W4vzAuFq8Bt_EDWI1","nickname":"测试用户","headimgurl":"https://developer.yonyou.com/uc_server/images/noavatar_big.gif"};
		}
//		userInfo = T.getPrefs('userInfo') || {"openid":"oemup55oyPn_W4vzAuFq8Bt_EDWI","nickname":"天天笑","headimgurl":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKVibpnjmtjfhj35l8D5l19fCHCCjMqLvib83Cm9C35DCC8ZB8jak06eHX8HNzj58HyK0v2vRgNzKBw/132"};
		if(userInfo){
			for(var _eItem in userInfo){
				that.pageParam[_eItem] = userInfo[_eItem];
			}
			$api.removeCls($api.byId("app"), "none");
			setTimeout(function(){
				try{that.init();}catch(e){console.error(e.message)}
			},1);
		}else{
			if(!that.pageParam.code){
				window.location.href = ("http://test.dc.cszysoft.com:21408/lzt/wechat/oauth?invokeUrl="+(window.location.href.replace(window.location.origin,'')));
			}else{
				T.ajax({u: "http://test.dc.cszysoft.com:21408/lzt/wechat/invoke?code=" + that.pageParam.code,dotSys:true}, '', function(ret, err) {
					if(ret){
						userInfo = JSON.parse(ret.data || "{}");
						if(userInfo.errcode){
							alert(JSON.stringify(userInfo));
							//window.location.replace("http://test.dc.cszysoft.com:21408/lzt/wechat/oauth?invokeUrl="+(window.location.href.replace(window.location.origin,'')));
						}else{
							T.setPrefs("userInfo",userInfo);
							for(var _eItem in userInfo){
								that.pageParam[_eItem] = userInfo[_eItem];
							}
							$api.removeCls($api.byId("app"), "none");
							setTimeout(function(){
								try{that.init();}catch(e){console.error(e.message)}
							},1);
						}
					}else{
						alert('请求鉴权code失败，请重试');
					}
				},'');
			}
		}
	},
	//在app中每个页面都会调用
	initApp:function(){
		var that = this;
		that.safeAreaTop = api.safeArea.top;//顶部高度
		that.safeAreaBottom = api.safeArea.bottom;//底下导航条高度
		that.isApp = true;
		that.changeConfiguration();
		//app 再刷新一次
		setTimeout(function(){
			try{that.init();}catch(e){console.error(e.message)}
		},1);
	},
	//接收网页端地址后面的参数 并加入到that.pageParam中去
	initPageParam:function(){
		var that = this;
		var datas = {};
		if(location.href.indexOf('?') == -1)return;//没有参数的时候
		var param = location.href.substr(location.href.indexOf('?')+1);
		if (param) {
			param = decodeURIComponent(decodeURI(param));
			var params = param.split("&");
			if (params) {
				for (var i = 0; i < params.length; i++) {
					if (params[i]) {
						var data_key = params[i].substring(0, params[i].indexOf("="));
						var data_value = params[i].substring(params[i].indexOf("=") + 1);
						if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
							data_value = JSON.parse(data_value);
						datas[data_key] = data_value;
					}
				}
			}
		}
		that.pageParam = datas;//页面参数
		if(that.pageParam.ndata){//新的入参参数
			var ndatas = that.pageParam.ndata.split(that.pageParam.ndata.indexOf('-zyz-') != -1?'-zyz-':'-@-');
			ndatas.forEach(function(_eItem,_eIndex,_eArr){
				that.pageParam[_eItem.split('-zy-')[0]] = _eItem.split('-zy-')[1];
			});
			delete that.pageParam.ndata;//删除原始数据 
			if(that.pageParam.sysUrl){
				T.setPrefs('SYS_app_url',that.pageParam.sysUrl);//存一下请求url
			}
			if(that.pageParam.sysAreaId){
				T.setPrefs('SYS_SiteID',that.pageParam.sysAreaId);//存一下请求url
			}
			if(that.pageParam.token){
				T.setPrefs('Sys_token',that.pageParam.token);
			}
			that.iszx = that.pageParam.iszx == "true";
			T.setPrefs('appTheme',that.pageParam.appTheme);
		}
		//获取页面历史数据
		var pathname = window.location.pathname;
		that.pathname = pathname.substring(pathname.lastIndexOf('/')+1,pathname.lastIndexOf('.'));
		that.refreshPageSize = Number(window.sessionStorage.getItem((that.pathname+"_refreshPageSize_"+(that.pageParam.recordId||"")) || "0"));
		that.seachText = window.sessionStorage.getItem((that.pathname+"_seachText_"+(that.pageParam.recordId||"")) || "");
	},
	init:function(){},
	//消除IOS默认300延时
	cleanIosDelay:function(){
		var that = this;
		setTimeout(function(){
			if(T.systemType() == "ios"){
//				try{FastClick.attach(document.body);}catch(e){console.error(e.message)}
			}
		},10);
	},
	//全局配置
	changeConfiguration:function(){
		var that = this;
		try{
			that.appFont = api.getPrefs({sync:true,key:'appFont'}) || "微软雅黑";
		}catch(e){
			that.appFont = T.getPrefs("appFont") || "微软雅黑";
		}
		try{
			that.appFontSize = Number(api.getPrefs({sync:true,key:'appFontSize'}) || 16);
		}catch(e){
			that.appFontSize = Number(T.getPrefs("appFontSize") || 16);
		}
		try{
			that.appTheme = that.pageParam.appTheme || api.getPrefs({sync:true,key:'appTheme'}) || "#007BFF";
		}catch(e){
			that.appTheme = that.pageParam.appTheme || T.getPrefs("appTheme") || "#007BFF";
		}
	},
	//样式加载配置	字体不能改	大小传过来 +几		主题色不能改
	loadConfiguration:function(_changeSize,_max){
		var that = this;
		var changeSize = _changeSize || 0;
		return "font-size:"+((that.appFontSize>_max?_max:that.appFontSize+changeSize)*0.01)+"rem;font-family:"+that.appFont+";";
	},
	//根据配置大小	设置按钮 图片等宽高 +-N
	loadConfigurationSize:function(_changeSize,_who){
		var that = this;
		var changeSize = _changeSize || 0;
		var returnCss = "";
		var cssWidth,cssHeight;
		if(T.isArray(_changeSize)){
			cssHeight = "height:"+((that.appFontSize+(_changeSize[1] || 0))*0.01)+"rem;";
			cssWidth = "width:"+((that.appFontSize+(_changeSize[0] ||0 ))*0.01)+"rem;";
		}else{
			cssWidth = "width:"+((that.appFontSize+changeSize)*0.01)+"rem;";
			cssHeight = "height:"+((that.appFontSize+changeSize)*0.01)+"rem;";
		}
		if(!_who){
			returnCss = cssWidth + cssHeight;
		}else{
			returnCss = _who == 'w'?cssWidth:cssHeight;
		}
		return returnCss;
	},
	// ================================================================================
	//获取item	只有一层级的时候 会返回 当前index	_i
	getItemForKey:function(_value,_list,_key,_child){
		var that = this;
		var hasChild = false;
		for(var i = 0; i < _list.length; i++){
			if(T.isArray(_list[i])){
				hasChild = true;
				var result = that.getItemForKey(_value,_list[i],_key,true);
				if(result)//看对象里面有没有结果	没有就不管
					return result;
			}else{
				if((T.isString(_list[i])?_list[i]:_list[i][_key||"key"]) == _value){
					if(!T.isString(_list[i]))
						_list[i]['_i'] = i;
					return _list[i];
				}
			}
		}
		if(!_child && !hasChild)
			return false;
	},
	//在集合中删除第一个参数obj和index都可以或对比字符串	第二个传入集合	第三个为对比key
	delItemForKey:function(_obj,_list,_key){
		var that = this;
		if(!T.isParameters(_obj))return;
		if(T.isTargetType(_obj,'number')){//如果是数字 
			_list.splice(_obj,1);//删除起始角标	 /	几个数量
		}else{
			var contrastObj = T.isString(_obj)?_obj:_obj[_key||"url"];
			for (var i = 0; i < _list.length; i++) {
				if((T.isString(_list[i])?_list[i]:_list[i][_key||"url"]) == contrastObj)
					_list.splice(i,1);
			}
		}
	},
	//防止报错
	cacheImg:function(){
		
	},
	//处理原生格式为html格式	一般用于提交时 
	conversionRichText:function(value) {
		if (!value || !T.isString(value))return value;
		var textList = value.split('\n');
		var str = '';
		for (var i = 0; i < textList.length; i++) {
			var addText = textList[i].replace(/&amp;/g,"&").replace(/ /g,"&nbsp;");
			if (addText) {
				str = str + '<p>' + addText + '</p>';
			}
		}
		return str;
	},
	//打开预览图片 需要item带url的list 和当前第几页 没传就第1页
	previewImg: function(_list, _index, _key) {
		var that = this;
		var images = [];
		var nowIndex = _index;
		if(T.isArray(_list)){
			for (var i = 0; i < _list.length; i++) {
				images.push(_list[i][_key || "url"]);
			}
		}else{
			images.push(_list[_key || "url"]);
			nowIndex = 0;
		}
		if(T.isAPICloud()){
			T.openImageBrowser({imageUrls:images,activeIndex:nowIndex});
		}else{
			vant.ImagePreview({images: images,startPosition:nowIndex});
		}
	},
	//通用列表页面点击 搜索确定事件
	//	@input 输入中监听			@keyup.enter 回车
	btnSearch:function(_type){
		if(!_type)
			this.$refs["btnSearch"].blur();
		T.goPageTop();
		try{
			this.getData(0);
		}catch(e){console.error(e.message)}
	},
	//网页版下拉刷新功能		自己页面不引用 使用默认调用刷新列表 否则自己处理 3秒还原================================================================================
	onRefresh:function(){
		var that = this;
		if(that.appRefresh.isDisabled || that.appRefreshisDisabled){
			return;
		}
		that.appRefresh.isLoading = true;
		that.pageNo = 1;
		that.onLoad();
	},
	//vue中监听滚动	适用 overflow: auto;
	scrollEvent:function(e){
		var that = this;
		var scrollTop = e.srcElement.scrollTop;//离顶部的距离
		that.scrollTop = scrollTop;
		if(scrollTop > 0){
			that.appRefresh.isDisabled = true;
		}else{
			that.appRefresh.isDisabled = false;
		}
	},
	//列表加载方法
	onLoad:function(){
		var that = this;
		console.log('你还未设置列表加载请求，请在代码中添加 onLoad 并处理');
		setTimeout(function(){
			T.refreshHeaderLoadDone();
		},1000);
	},
	//文字共用方法集合 ================================================================================
	//处理正文中的原生格式问题 转换成html格式
	dealWithCon: function(_content){
		var that = this;
		//抓到的数据 可能没得标签
		var expText = _content || "";
		//历史正文没得 标签 先加一个
		var expLength = expText.match(/<.*?>/g) || [];
		if(expLength.length < 2){
			expText = '<p>' + expText + '</p>';
		}
		//再匹配标签中的正文
		var strRegex = '[^\>?]+[\<?]';
		var regex = new RegExp(strRegex,"gi");//匹配所有非标签中的字符	g不停止	 i忽略大小写
		if(regex.test(expText)){//如果有标签内容	(肯定有)
			expText = expText.replace(regex,function(m){
				return m.replace(/\n/g,"<br/>").replace(/ /g,"&nbsp;");//将标签中的	\n换成br 空格 抱成 nbsp
			});
		}
		return expText;
	},
	//打开附件
	annexOpen: function (_item) {
		var that = this;
		var openUrl = zyUrl.getAppUrl() + "file/preview/" + _item.url;
		T.showProgress("打开中");
		T.ajax({ u: "https://www.yozodcs.com/fcscloud/file/http?",dotSys:true }, 'onlinefile', function (ret, err) {
			if (ret) {
				var data = (ret.data || {}).data;
				if (data) {
					T.ajax({ u: "https://www.yozodcs.com/fcscloud/composite/convert?",dotSys:true }, 'onlinefile', function (ret, err) {
						T.hideProgress();
						var viewUrl = (ret.data || {}).viewUrl || "";
						if (viewUrl) {
							T.openWin('',viewUrl);
						} else {
							T.toast(ret.message || "打开失败，请重试");
						}
					}, "生成链接", "post", {
						srcRelativePath:data,
						convertType:that.getFileTypeAttr(data.substring(data.lastIndexOf('.'))).convertType,
						isDccAsync:1,
						isCopy:0,
						noCache:0,
						fileUrl:openUrl,
						showFooter:0,//是否显示页脚
						isHeaderBar:0,
						htmlName:_item.name,
						htmlTitle:_item.name,
					});
				} else {
					T.hideProgress();
					T.toast(ret.message || "打开失败，请重试");
				}
			} else {
				T.hideProgress();
				T.toast("打开失败");
			}
		}, "在线转换", "post", {
			fileUrl: openUrl
		});

	},
	//获取 文件类型的各种属性
	getFileTypeAttr: function (_type) {
		var type = _type || "";
		type = type.toLocaleLowerCase(), iconInfo = { name: "icon_unknown.png", type: "unknown" };
		try {
			if (type.indexOf('.') != -1) type = type.split('.')[type.split('.').length - 1];
			switch (type) {
				case 'xlsx': case 'xlsm': case 'xlsb': case 'xltx': case 'xltm': case 'xls': case 'xlt': case 'et': case 'csv': case 'uos'://excel格式
					iconInfo.name = "icon_excel.png";
					iconInfo.type = "excel";
					iconInfo.convertType = "61";
					break;
				case 'doc': case 'docx': case 'docm': case 'dotx': case 'dotm': case 'dot': case 'xps': case 'rtf': case 'wps': case 'wpt': case 'uot'://word格式
					iconInfo.name = "icon_word.png";
					iconInfo.type = "word";
					iconInfo.convertType = "61";
					break;
				case 'pdf'://pdf格式
					iconInfo.name = "icon_pdf.png";
					iconInfo.type = "pdf";
					iconInfo.convertType = "20";
					break;
				case 'ppt': case 'pptx': case 'pps': case 'pot': case 'pptm': case 'potx': case 'potm': case 'ppsx': case 'ppsm': case 'ppa': case 'ppam': case 'dps': case 'dpt': case 'uop'://ppt
					iconInfo.name = "icon_ppt.png";
					iconInfo.type = "ppt";
					iconInfo.convertType = "61";
					break;
				case 'bmp': case 'gif': case 'jpg': case 'pic': case 'png': case 'tif': case 'jpeg': case 'jpe': case 'icon': case 'jfif': case 'dib'://图片格式
					iconInfo.name = "icon_pic.png";
					iconInfo.type = "image";
					iconInfo.convertType = "23";
					break;
				case 'txt'://文本
					iconInfo.name = "icon_txt.png";
					iconInfo.type = "txt";
					iconInfo.convertType = "61";
					break;
				case "rar": case "zip": case "7z": case "tar": case "gz": case "jar": case "ios"://压缩格式
					iconInfo.name = "icon_zip.png";
					iconInfo.type = "compression";
					iconInfo.convertType = "19";
					break;
				case "mp4": case "avi": case "flv": case "f4v": case "webm": case "m4v": case "mov": case "3gp": case "rm": case "rmvb": case "mkv"://视频格式
					iconInfo.name = "icon_mp4.png";
					iconInfo.type = "video";
					break;
				case "mp3": case "amr": case "pcm": case "wav": case "aiff": case "aac": case "ogg": case "wma": case "flac": case "alac": case "wma": case "cda"://音频格式
					iconInfo.name = "icon_mp3.png";
					iconInfo.type = "voice";
					break;
			}
		} catch (e) { console.error(e.message) }
		return iconInfo;
	},
	//适配app 和pc页面
	fitWidth:function(){
		var that = this;
		if(IsPC()){
			$api.css($api.dom("body"),"max-width:600px;margin:auto;");
		}else{
			$api.css($api.dom("body"),"max-width:none;margin:auto;");
		}
	},
};
var nvmData = {},nvmWatch = {},nvmMethods = {};
T.ready(function(){
	try{
		nvmData = T.setNewJSON(DEFAULT_DATA,vmData);
		nvmWatch = T.setNewJSON(DEFAULT_WATCH,vmWatch);
		nvmMethods = T.setNewJSON(DEFAULT_METHODS,vmMethods);
	}catch(e){console.error(e.message)}
	vm = new Vue({
		el: '#app',
		data: nvmData,
		watch:nvmWatch,
		mounted: function () {//页面元素加载完成时调用
			var that = this;
				try{that.defalutInit();}catch(e){console.error(e.message)}
		},
		methods: nvmMethods
	});
});


function IsPC () {
	var userAgentInfo = navigator.userAgent;
	var Agents = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"];
	var flag = true;
	for (var v = 0; v < Agents.length; v++) {
		if (userAgentInfo.indexOf(Agents[v]) > 0) {
			flag = false;
			break;
		}
	}
	return flag;
}

window.onresize=function(){
	vm.fitWidth();
}

var scrollTimer;	 // 要保证最后要运行一次 做加载更多处理
window.onscroll = function () {
	if (scrollTimer != undefined) {
		window.clearTimeout(scrollTimer);
	}
	scrollTimer = window.setTimeout(function () {
		vm.scrollTop = getDocumentTop();//当前划动高度
		if(getScrollHeight() <= getDocumentTop() + getWindowHeight() + 100){//处理 列表页面加载更多
			vm.loadMore && vm.loadMore();
		}
		if(window.noScroll){//列表没加载成功 不进入保存
			//保存当前划动位置 普通单页面 增加上当前页面recordId 保证页面唯一性
			window.sessionStorage.setItem(vm.pathname+"_scrollHeight_"+(vm.pageParam.recordId||""),vm.scrollTop);
		}
	}, 100);
}
//页面加载完后 回显到划动位置
function scrollToView(timeout){
	var scrollHeight = window.sessionStorage.getItem(vm.pathname+"_scrollHeight_"+(vm.pageParam.recordId||""));
	if(scrollHeight){
		setTimeout(function(){
			window.scrollTo({
				top: Number(scrollHeight),
//					behavior: "smooth"//带动画划动
			});
		}, 0);
	}
}

//文档高度
function getDocumentTop() {
	var scrollTop =	0, bodyScrollTop = 0, documentScrollTop = 0;
	if (document.body) {
		bodyScrollTop = document.body.scrollTop;
	}
	if (document.documentElement) {
		documentScrollTop = document.documentElement.scrollTop;
	}
	scrollTop = (bodyScrollTop - documentScrollTop > 0) ? bodyScrollTop : documentScrollTop;
	return scrollTop;
}

//可视窗口高度
function getWindowHeight() {
	var windowHeight = 0;
	if (document.compatMode == "CSS1Compat") {
		windowHeight = document.documentElement.clientHeight;
	} else {
		windowHeight = document.body.clientHeight;
	}
	return windowHeight;
}

//滚动条滚动高度
function getScrollHeight() {
	var scrollHeight = 0, bodyScrollHeight = 0, documentScrollHeight = 0;
	if (document.body) {
			bodyScrollHeight = document.body.scrollHeight;
	}
	if (document.documentElement) {
			documentScrollHeight = document.documentElement.scrollHeight;
	}
	scrollHeight = (bodyScrollHeight - documentScrollHeight > 0) ? bodyScrollHeight : documentScrollHeight;
	return scrollHeight;
}