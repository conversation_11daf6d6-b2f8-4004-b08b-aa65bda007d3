<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
		<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
		<title></title>
		<style>
			html,body{padding:0;margin: 0;width:100%;height:100%;display: flex;align-items: flex-start;justify-content: center;}
		</style>
	</head>
<body>
	<div id="qrContainer" style="width: 100%;height:auto;max-height: 100%;"></div>
</body>
<script type="text/javascript" src="./script/html5-qrcode.min.js"></script>
<script type="text/javascript">
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

	function onApiLoaded(){
		var html5QrcodeScanner = new Html5Qrcode("qrContainer");
		html5QrcodeScanner.start(
			{facingMode:"environment"}//environment:后摄像头；user:前摄像头
			,{ fps: 10, qrbox:{width:200,height:200}},function onScanSuccess(decodedText, decodedResult) {
				html5QrcodeScanner.pause();
				html5QrcodeScanner.stop();
				sendFrameMsg({result:decodedText},"setData");
			});
	}

	window.onload = function() {
		onApiLoaded();
	}

	window.addEventListener('message', function(event) {
		msgData = event.data;
		console.log(msgData);
		switch(msgData.mType){
			case "init":
				window.initData = msgData;
				
				break;
		}
	});

	function sendFrameMsg(_obj,_type){
		var sendMsg = {};
		if(typeof _obj === "object"){
			for(var value in _obj){
				if(typeof _obj[value] != "function"){
					sendMsg[value] = _obj[value];
				}
			}
		}
		sendMsg.mType = _type;
		window.parent.postMessage(sendMsg, '*');
	}
</script>
</html>