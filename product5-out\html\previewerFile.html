<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>详情</title>
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app,iframe{ width: 100%; height: 100%; margin: 0; padding: 0;position: absolute;transform: translateZ(0);will-change: transform;}
		@keyframes circle {
		  0% {
			transform: rotate(0);
		  }
		  100% {
			transform: rotate(360deg);
		  }
		}
		.loadingBox{
			width:100%;
			height:100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.loading {
		  width: 30px;
		  height: 30px;
		  border: 2px solid #333;
		  border-top-color: transparent;
		  border-radius: 100%;

		  animation: circle infinite 0.75s linear;
		}
	</style>
</head>
<body>
	<div v-cloak id="app">
		<div v-if="loadingType==0" class="loadingBox"><div class="loading"></div></div>
		<div v-else-if="loadingType==2" class="loadingBox"><div style="color:#333;font-size:16px;word-wrap: break-word;max-width: 100%;max-height: 100%;">{{errText}}</div></div>
		<iframe v-else-if="previewUrl" name="preview" id="preview" :src="previewUrl" frameborder="0"></iframe>
	</div>
</body>
<script type="text/javascript" src="./script/vue.min.js"></script>
<script type="text/javascript" src="./script/axios.min.js"></script>
<script type="text/javascript" src="./script/url.js"></script>
<script type="module">
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var appId = "yozoFi3j3Esd2850",appKey = "4d4dfc89c15aaca72a7f3e386ac3";
	var vm,pageParam={};
	var vmData = {
		appUrl:appUrl,
		appId:datas.appId||appId,
		appKey:datas.appKey||appKey,
		loadingType:0,
		errText:"",
		previewUrl:"",
	};
	var methods = {
		init:async function(){
			var that = this;
			that.loadingType = 0;
			
			// 1. 下载远程文件时添加请求头
			
			//const response = await getFile();
			//if(!response){
			//	return;
			//}
			
			// 2. 创建一个 Blob 对象
			//const fileBlob = new Blob([response.data], { type: response.data.type });

			var protocol = window.location.protocol;
			if(protocol.indexOf('http') != 0){
				protocol = "https:";
			}
			// 3. 创建 FormData 对象，并将 Blob 添加到其中
			//const formData = new FormData();
			//formData.append('file', fileBlob);
			//formData.append('appId', that.appId);
			//formData.append('sign', generateSign(that.appKey, {"appId":[that.appId]}));
			axios.post(protocol+"//dmc.yozocloud.cn/api/file/http",that.getPostParam({
				appId:that.appId,
				fileUrl:location.href.split('fileUrl=')[1]
			}),
			{ headers: {'Content-Type': 'application/x-www-form-urlencoded'} }).catch(function (err) {
				that.loadingType = 2;
				that.errText = JSON.stringify(err);
			}).then(function (ret) {
				ret = ret.data || {};
				if(ret.errorcode == 0){
					var data = ret.data||{};
					//预览参数
					var previewParam = that.getPostParam({
						fileVersionId:data.fileVersionId,
						appId:that.appId,
						
					});
					var previewParams = "";
					for (let key in previewParam) {
						previewParams += `&${key}=${previewParam[key]}`;
					}
					that.loadingType = 1;
					that.previewUrl = `${protocol}//eic.yozocloud.cn/api/view/file?${previewParams}`;
				}else{
					that.loadingType = 2;
					that.errText = ret.message || ret.data;
				}
			});
		},
		getPostParam:function(_param){
			var that = this;
			var rParam = {};
			var nParam = {};
			for (let key in _param) {
				rParam[key] = _param[key];
				nParam[key] = [_param[key]];
			}
			rParam.sign = generateSign(that.appKey, nParam)
			return rParam;
		},
		
	};
	
	async function getFile() {
		try {
			const response = await axios.get(datas.fileUrl, {
			  responseType: 'blob', // 确保接收的是二进制文件
			  headers: {Authorization:datas.token} // 在这里添加请求头
			});
			return response;
		} catch (error) {
			console.error(error);
		}
		return null;
	}

	window.onload = function() {
		showVue();
	};
	
	function showVue(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
	function generateSign(secret, params) {
		var fullParamStr = uniqSortParams(params);
		return hmacSHA256(fullParamStr, secret);
	}

	function uniqSortParams(params) {
		delete params.sign;

		var var5 = [];
		var var6 = 0;
		for (var key in params) {
			var5[var6] = key;
			var6++;
		}
		var5.sort(function (a, b) {
			return a.localeCompare(b, 'zh-CN');
		});

		var result = "";
		for (var var7 = 0; var7 < var5.length; var7++) {
			var key = var5[var7]
			var var8 = params[key];
			var8.sort(function (a, b) {
				return a.localeCompare(b, 'zh-CN');
			});

			if (var8 != null && var8.length > 0) {
				for (var var9 = 0; var9 < var8.length; var9++) {
					result += key + "=" + var8[var9];
				}
			} else {
				result += key + "=";
			}
		}
		return result;
	}

	function hmacSHA256(data, key) {
		data != null ? data : "";
		var var2 = CryptoJS.HmacSHA256(data, key);
		var var3 = var2.toString(CryptoJS.enc.Hex);
		return var3.toUpperCase();
	}
</script>
</html>